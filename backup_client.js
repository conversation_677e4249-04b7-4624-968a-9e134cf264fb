/* ملف JavaScript احتياطي للنظام المحاسبي */
/* تاريخ الإنشاء: 2025-08-22 */

// وظائف أساسية للنظام
(function() {
    'use strict';
    
    // تهيئة النظام عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل النظام المحاسبي - النظام القضائي');
        initializeSystem();
    });
    
    // تهيئة النظام
    function initializeSystem() {
        try {
            // تهيئة الجداول
            initializeTables();
            
            // تهيئة النماذج
            initializeForms();
            
            // تهيئة الأزرار
            initializeButtons();
            
            // تهيئة التحقق من الصحة
            initializeValidation();
            
            console.log('تم تهيئة النظام بنجاح');
        } catch (error) {
            console.error('خطأ في تهيئة النظام:', error);
        }
    }
    
    // تهيئة الجداول
    function initializeTables() {
        const tables = document.querySelectorAll('.table');
        tables.forEach(function(table) {
            // إضافة فئات Bootstrap
            table.classList.add('table-striped', 'table-hover');
            
            // إضافة إمكانية الفرز
            const headers = table.querySelectorAll('th');
            headers.forEach(function(header, index) {
                if (header.textContent.trim()) {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', function() {
                        sortTable(table, index);
                    });
                }
            });
        });
    }
    
    // فرز الجدول
    function sortTable(table, columnIndex) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort(function(a, b) {
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();
            
            // محاولة المقارنة كأرقام أولاً
            const aNum = parseFloat(aText);
            const bNum = parseFloat(bText);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return aNum - bNum;
            }
            
            // المقارنة كنص
            return aText.localeCompare(bText, 'ar');
        });
        
        // إعادة ترتيب الصفوف
        rows.forEach(function(row) {
            tbody.appendChild(row);
        });
    }
    
    // تهيئة النماذج
    function initializeForms() {
        const forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            // إضافة معالج الإرسال
            form.addEventListener('submit', function(e) {
                if (!validateForm(form)) {
                    e.preventDefault();
                    showAlert('يرجى تصحيح الأخطاء في النموذج', 'danger');
                }
            });
            
            // إضافة التحقق المباشر
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(function(input) {
                input.addEventListener('blur', function() {
                    validateField(input);
                });
            });
        });
    }
    
    // تهيئة الأزرار
    function initializeButtons() {
        // أزرار الحذف
        const deleteButtons = document.querySelectorAll('.btn-danger, [data-action="delete"]');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                if (!confirm('هل أنت متأكد من الحذف؟')) {
                    e.preventDefault();
                }
            });
        });
        
        // أزرار التحميل
        const loadingButtons = document.querySelectorAll('[data-loading]');
        loadingButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                showLoading(button);
            });
        });
    }
    
    // تهيئة التحقق من الصحة
    function initializeValidation() {
        // إضافة قواعد التحقق المخصصة
        const requiredFields = document.querySelectorAll('[required]');
        requiredFields.forEach(function(field) {
            field.addEventListener('invalid', function() {
                field.setCustomValidity('هذا الحقل مطلوب');
            });
            
            field.addEventListener('input', function() {
                field.setCustomValidity('');
            });
        });
    }
    
    // التحقق من صحة النموذج
    function validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(function(input) {
            if (!validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    // التحقق من صحة الحقل
    function validateField(field) {
        let isValid = true;
        const value = field.value.trim();
        
        // إزالة رسائل الخطأ السابقة
        removeFieldError(field);
        
        // التحقق من الحقول المطلوبة
        if (field.hasAttribute('required') && !value) {
            showFieldError(field, 'هذا الحقل مطلوب');
            isValid = false;
        }
        
        // التحقق من البريد الإلكتروني
        if (field.type === 'email' && value && !isValidEmail(value)) {
            showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
            isValid = false;
        }
        
        // التحقق من الأرقام
        if (field.type === 'number' && value && isNaN(value)) {
            showFieldError(field, 'يرجى إدخال رقم صحيح');
            isValid = false;
        }
        
        return isValid;
    }
    
    // عرض خطأ الحقل
    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }
    
    // إزالة خطأ الحقل
    function removeFieldError(field) {
        field.classList.remove('is-invalid');
        
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
    
    // التحقق من صحة البريد الإلكتروني
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // عرض رسالة تنبيه
    function showAlert(message, type) {
        type = type || 'info';
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        // إدراج الرسالة في أعلى الصفحة
        const container = document.querySelector('.container') || document.body;
        container.insertBefore(alertDiv, container.firstChild);
        
        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(function() {
            alertDiv.remove();
        }, 5000);
    }
    
    // عرض مؤشر التحميل
    function showLoading(button) {
        const originalText = button.textContent;
        button.textContent = 'جاري التحميل...';
        button.disabled = true;
        
        // إعادة تفعيل الزر بعد 3 ثوان (يمكن تخصيصه)
        setTimeout(function() {
            button.textContent = originalText;
            button.disabled = false;
        }, 3000);
    }
    
    // وظائف مساعدة للنظام القضائي
    window.LegalSystem = {
        // تحديث حالة القضية
        updateCaseStatus: function(caseId, status) {
            console.log(`تحديث حالة القضية ${caseId} إلى ${status}`);
            // يمكن إضافة AJAX call هنا
        },
        
        // إضافة متابعة جديدة
        addFollow: function(caseId, followData) {
            console.log(`إضافة متابعة للقضية ${caseId}`, followData);
            // يمكن إضافة AJAX call هنا
        },
        
        // تصدير البيانات
        exportData: function(format) {
            console.log(`تصدير البيانات بصيغة ${format}`);
            showAlert('جاري تصدير البيانات...', 'info');
        },
        
        // طباعة التقرير
        printReport: function() {
            window.print();
        }
    };
    
    // إضافة وظائف عامة للنافذة
    window.showAlert = showAlert;
    window.showLoading = showLoading;
    
    console.log('تم تحميل ملف JavaScript الاحتياطي للنظام المحاسبي');
    
})();

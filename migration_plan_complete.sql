-- خطة الهجرة الشاملة لنقل نظام القضايا إلى نظام mohhash
-- تاريخ الإنشاء: 2025-08-21
-- الهدف: دمج جميع جداول النظام القضائي مع النظام المحاسبي

-- =====================================================
-- المرحلة الأولى: إنشاء الجداول الأساسية
-- =====================================================

-- 1. جدول المحافظات
CREATE TABLE IF NOT EXISTS governorates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(10),
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. جدول المحاكم
CREATE TABLE IF NOT EXISTS courts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    governorate_id INTEGER REFERENCES governorates(id),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. جدول الفروع
CREATE TABLE IF NOT EXISTS branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    governorate_id INTEGER REFERENCES governorates(id),
    address TEXT,
    phone VARCHAR(20),
    manager_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. جدول أنواع القضايا
CREATE TABLE IF NOT EXISTS issue_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
);

-- 5. جدول الخدمات
CREATE TABLE IF NOT EXISTS services (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    default_percentage DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
);

-- 6. جدول النسب المالية
CREATE TABLE IF NOT EXISTS lineages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    admin_percentage DECIMAL(5,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);

-- =====================================================
-- المرحلة الثانية: جداول القضايا والمتابعة
-- =====================================================

-- 7. جدول القضايا (محدث ومحسن)
CREATE TABLE IF NOT EXISTS issues (
    id SERIAL PRIMARY KEY,
    case_number VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    client_id INTEGER, -- ربط مع جدول العملاء الموجود
    client_name VARCHAR(255),
    court_id INTEGER REFERENCES courts(id),
    court_name VARCHAR(255),
    issue_type_id INTEGER REFERENCES issue_types(id),
    issue_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'pending',
    amount DECIMAL(12,2) DEFAULT 0,
    contract_method VARCHAR(50) DEFAULT 'بالجلسة',
    contract_date DATE,
    next_hearing DATE,
    notes TEXT,
    account_id INTEGER, -- ربط مع النظام المحاسبي
    created_date DATE DEFAULT CURRENT_DATE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. جدول توزيع القضايا
CREATE TABLE IF NOT EXISTS case_distribution (
    id SERIAL PRIMARY KEY,
    issue_id INTEGER REFERENCES issues(id),
    lineage_id INTEGER REFERENCES lineages(id),
    distribution_date DATE DEFAULT CURRENT_DATE,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 9. جدول توزيع الخدمات
CREATE TABLE IF NOT EXISTS service_distributions (
    id SERIAL PRIMARY KEY,
    case_distribution_id INTEGER REFERENCES case_distribution(id),
    service_id INTEGER REFERENCES services(id),
    lawyer_id INTEGER, -- ربط مع جدول الموظفين
    percentage DECIMAL(5,2) DEFAULT 0,
    amount DECIMAL(12,2) DEFAULT 0,
    status VARCHAR(50) DEFAULT 'active',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. جدول المتابعات
CREATE TABLE IF NOT EXISTS follows (
    id SERIAL PRIMARY KEY,
    case_id INTEGER REFERENCES issues(id),
    service_id INTEGER REFERENCES services(id),
    user_id INTEGER, -- ربط مع جدول المستخدمين
    report TEXT NOT NULL,
    date_field DATE DEFAULT CURRENT_DATE,
    status VARCHAR(50) DEFAULT 'pending',
    next_hearing_id INTEGER,
    notes TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- المرحلة الثالثة: جداول النظام والإعدادات
-- =====================================================

-- 11. جدول الإعلانات
CREATE TABLE IF NOT EXISTS announcements (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    type VARCHAR(50) DEFAULT 'info',
    priority VARCHAR(20) DEFAULT 'normal',
    is_active BOOLEAN DEFAULT true,
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    target_users TEXT, -- JSON array of user IDs
    created_by INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 12. جدول إعدادات الذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS ai_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 13. جدول رسائل المحادثة
CREATE TABLE IF NOT EXISTS chat_messages (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    message TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'user', -- 'user' or 'ai'
    session_id VARCHAR(255),
    response_time INTEGER, -- milliseconds
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 14. جدول عناصر التنقل
CREATE TABLE IF NOT EXISTS navigation_items (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    href VARCHAR(500),
    icon VARCHAR(100),
    parent_id INTEGER REFERENCES navigation_items(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    required_permissions TEXT, -- JSON array
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- المرحلة الرابعة: الفهارس والقيود
-- =====================================================

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_issues_case_number ON issues(case_number);
CREATE INDEX IF NOT EXISTS idx_issues_client_id ON issues(client_id);
CREATE INDEX IF NOT EXISTS idx_issues_court_id ON issues(court_id);
CREATE INDEX IF NOT EXISTS idx_issues_status ON issues(status);
CREATE INDEX IF NOT EXISTS idx_follows_case_id ON follows(case_id);
CREATE INDEX IF NOT EXISTS idx_follows_user_id ON follows(user_id);
CREATE INDEX IF NOT EXISTS idx_case_distribution_issue_id ON case_distribution(issue_id);
CREATE INDEX IF NOT EXISTS idx_service_distributions_lawyer_id ON service_distributions(lawyer_id);

-- =====================================================
-- المرحلة الخامسة: البيانات الأولية
-- =====================================================

-- إدراج المحافظات الأساسية
INSERT INTO governorates (name, code) VALUES
('صنعاء', 'SA'),
('عدن', 'AD'),
('تعز', 'TA'),
('الحديدة', 'HD'),
('إب', 'IB'),
('ذمار', 'DH'),
('حضرموت', 'HA'),
('لحج', 'LA'),
('أبين', 'AB'),
('شبوة', 'SH')
ON CONFLICT (name) DO NOTHING;

-- إدراج أنواع القضايا الأساسية
INSERT INTO issue_types (name, description) VALUES
('قضايا مدنية', 'القضايا المدنية والتجارية'),
('قضايا جنائية', 'القضايا الجنائية والجزائية'),
('قضايا إدارية', 'القضايا الإدارية والحكومية'),
('قضايا أحوال شخصية', 'قضايا الزواج والطلاق والميراث'),
('قضايا عمالية', 'قضايا العمل والعمال'),
('قضايا تجارية', 'القضايا التجارية والاستثمار')
ON CONFLICT (name) DO NOTHING;

-- إدراج الخدمات الأساسية
INSERT INTO services (name, description, default_percentage) VALUES
('استشارة قانونية', 'تقديم الاستشارات القانونية', 10.00),
('صياغة عقود', 'صياغة ومراجعة العقود', 15.00),
('ترافع أمام المحاكم', 'التمثيل أمام المحاكم', 25.00),
('تحصيل ديون', 'تحصيل الديون والمستحقات', 20.00),
('تأسيس شركات', 'إجراءات تأسيس الشركات', 12.00)
ON CONFLICT (name) DO NOTHING;

-- إدراج إعدادات الذكاء الاصطناعي الأساسية
INSERT INTO ai_settings (setting_key, setting_value, description) VALUES
('ai_model', 'gpt-3.5-turbo', 'نموذج الذكاء الاصطناعي المستخدم'),
('response_delay', '2000', 'تأخير الرد بالميلي ثانية'),
('max_tokens', '150', 'الحد الأقصى للرموز في الرد'),
('temperature', '0.7', 'درجة الإبداع في الردود')
ON CONFLICT (setting_key) DO NOTHING;

-- إدراج عناصر التنقل الأساسية
INSERT INTO navigation_items (title, href, icon, sort_order) VALUES
('لوحة التحكم', '/dashboard', 'dashboard', 1),
('إدارة القضايا', '/legal', 'gavel', 2),
('النظام المحاسبي', '/accounting', 'calculator', 3),
('شؤون الموظفين', '/hr', 'users', 4),
('التقارير', '/reports', 'chart-bar', 5),
('الإعدادات', '/settings', 'cog', 6)
ON CONFLICT (title) DO NOTHING;

<?xml version="1.0" encoding="UTF-8"?>
<!-- نماذج النظام القضائي لنظام mohhash -->
<!-- تاريخ الإنشاء: 2025-08-21 -->

<!-- =====================================================
     نموذج إدارة القضايا
     ===================================================== -->
<form-module name="legal-issues" title="إدارة القضايا" icon="gavel">
    
    <!-- قائمة القضايا -->
    <list-view name="list" title="قائمة القضايا">
        <data-source>
            SELECT 
                i.id,
                i.case_number,
                i.title,
                i.client_name,
                c.name as court_name,
                it.name as issue_type,
                i.status,
                i.amount,
                i.contract_method,
                i.next_hearing,
                i.created_date
            FROM issues i
            LEFT JOIN courts c ON i.court_id = c.id
            LEFT JOIN issue_types it ON i.issue_type_id = it.id
            ORDER BY i.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120" sortable="true"/>
            <column name="title" title="عنوان القضية" width="250" sortable="true"/>
            <column name="client_name" title="اسم الموكل" width="150" sortable="true"/>
            <column name="court_name" title="المحكمة" width="150"/>
            <column name="issue_type" title="نوع القضية" width="120"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="amount" title="المبلغ" width="120" format="currency"/>
            <column name="contract_method" title="طريقة التعاقد" width="100"/>
            <column name="next_hearing" title="الجلسة القادمة" width="120" format="date"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <filters>
            <filter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="active">نشطة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </filter>
            <filter name="issue_type_id" title="نوع القضية" type="lookup" 
                   lookup-table="issue_types" lookup-field="name"/>
            <filter name="court_id" title="المحكمة" type="lookup" 
                   lookup-table="courts" lookup-field="name"/>
        </filters>
        
        <actions>
            <action name="add" title="إضافة قضية جديدة" icon="plus" color="primary"/>
            <action name="edit" title="تعديل" icon="edit" color="warning"/>
            <action name="delete" title="حذف" icon="delete" color="danger" confirm="true"/>
            <action name="view" title="عرض التفاصيل" icon="eye" color="info"/>
            <action name="distribute" title="توزيع القضية" icon="share-alt" color="success"/>
        </actions>
    </list-view>
    
    <!-- نموذج إضافة/تعديل القضية -->
    <form-view name="add" title="إضافة قضية جديدة">
        <tabs>
            <tab name="basic" title="البيانات الأساسية">
                <fields>
                    <field name="case_number" title="رقم القضية" type="text" required="true" 
                           validation="unique" placeholder="مثال: 2025/001"/>
                    <field name="title" title="عنوان القضية" type="text" required="true" 
                           placeholder="عنوان مختصر للقضية"/>
                    <field name="description" title="وصف القضية" type="textarea" rows="4" 
                           placeholder="وصف تفصيلي للقضية"/>
                    <field name="client_id" title="الموكل" type="lookup" 
                           lookup-table="clients" lookup-field="name" lookup-display="name + ' - ' + phone"/>
                    <field name="client_name" title="اسم الموكل (يدوي)" type="text" 
                           placeholder="في حالة عدم وجود الموكل في القائمة"/>
                </fields>
            </tab>
            
            <tab name="legal" title="البيانات القانونية">
                <fields>
                    <field name="court_id" title="المحكمة" type="lookup" 
                           lookup-table="courts" lookup-field="name"/>
                    <field name="court_name" title="اسم المحكمة (يدوي)" type="text" 
                           placeholder="في حالة عدم وجود المحكمة في القائمة"/>
                    <field name="issue_type_id" title="نوع القضية" type="lookup" 
                           lookup-table="issue_types" lookup-field="name" required="true"/>
                    <field name="status" title="حالة القضية" type="select" default="pending">
                        <options>
                            <option value="pending">معلقة</option>
                            <option value="active">نشطة</option>
                            <option value="completed">مكتملة</option>
                            <option value="cancelled">ملغية</option>
                        </options>
                    </field>
                    <field name="next_hearing" title="تاريخ الجلسة القادمة" type="date"/>
                </fields>
            </tab>
            
            <tab name="financial" title="البيانات المالية">
                <fields>
                    <field name="amount" title="مبلغ القضية" type="decimal" format="currency" 
                           placeholder="0.00"/>
                    <field name="contract_method" title="طريقة التعاقد" type="select" default="بالجلسة">
                        <options>
                            <option value="بالجلسة">بالجلسة</option>
                            <option value="مقطوع">مقطوع</option>
                            <option value="نسبة">نسبة من المبلغ</option>
                            <option value="مختلط">مختلط</option>
                        </options>
                    </field>
                    <field name="contract_date" title="تاريخ التعاقد" type="date"/>
                    <field name="notes" title="ملاحظات مالية" type="textarea" rows="3"/>
                </fields>
            </tab>
        </tabs>
        
        <save-action>
            INSERT INTO issues (
                case_number, title, description, client_id, client_name,
                court_id, court_name, issue_type_id, status, amount,
                contract_method, contract_date, next_hearing, notes
            ) VALUES (
                @case_number, @title, @description, @client_id, @client_name,
                @court_id, @court_name, @issue_type_id, @status, @amount,
                @contract_method, @contract_date, @next_hearing, @notes
            )
        </save-action>
        
        <update-action>
            UPDATE issues SET
                case_number = @case_number,
                title = @title,
                description = @description,
                client_id = @client_id,
                client_name = @client_name,
                court_id = @court_id,
                court_name = @court_name,
                issue_type_id = @issue_type_id,
                status = @status,
                amount = @amount,
                contract_method = @contract_method,
                contract_date = @contract_date,
                next_hearing = @next_hearing,
                notes = @notes,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = @id
        </update-action>
    </form-view>
</form-module>

<!-- =====================================================
     نموذج متابعة القضايا
     ===================================================== -->
<form-module name="case-follows" title="متابعة القضايا" icon="calendar">
    
    <list-view name="list" title="قائمة المتابعات">
        <data-source>
            SELECT 
                f.id,
                i.case_number,
                i.title as case_title,
                s.name as service_name,
                f.report,
                f.date_field,
                f.status,
                e.name as user_name,
                f.created_date
            FROM follows f
            JOIN issues i ON f.case_id = i.id
            JOIN services s ON f.service_id = s.id
            LEFT JOIN employees e ON f.user_id = e.id
            ORDER BY f.date_field DESC, f.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="case_title" title="عنوان القضية" width="200"/>
            <column name="service_name" title="نوع الخدمة" width="150"/>
            <column name="report" title="تقرير المتابعة" width="300" truncate="50"/>
            <column name="date_field" title="تاريخ المتابعة" width="120" format="date"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="user_name" title="المستخدم" width="120"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="datetime"/>
        </columns>
        
        <filters>
            <filter name="case_id" title="القضية" type="lookup" 
                   lookup-table="issues" lookup-field="case_number + ' - ' + title"/>
            <filter name="service_id" title="نوع الخدمة" type="lookup" 
                   lookup-table="services" lookup-field="name"/>
            <filter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </filter>
            <filter name="date_from" title="من تاريخ" type="date"/>
            <filter name="date_to" title="إلى تاريخ" type="date"/>
        </filters>
        
        <actions>
            <action name="add" title="إضافة متابعة جديدة" icon="plus" color="primary"/>
            <action name="edit" title="تعديل" icon="edit" color="warning"/>
            <action name="delete" title="حذف" icon="delete" color="danger" confirm="true"/>
            <action name="view" title="عرض التفاصيل" icon="eye" color="info"/>
        </actions>
    </list-view>
    
    <form-view name="add" title="إضافة متابعة جديدة">
        <fields>
            <field name="case_id" title="القضية" type="lookup" required="true"
                   lookup-table="issues" lookup-field="case_number + ' - ' + title" 
                   lookup-display="case_number + ' - ' + title + ' (' + client_name + ')'"/>
            <field name="service_id" title="نوع الخدمة" type="lookup" required="true"
                   lookup-table="services" lookup-field="name"/>
            <field name="report" title="تقرير المتابعة" type="textarea" required="true" rows="6"
                   placeholder="اكتب تقرير مفصل عن المتابعة..."/>
            <field name="date_field" title="تاريخ المتابعة" type="date" default="today"/>
            <field name="status" title="حالة المتابعة" type="select" default="pending">
                <options>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </field>
            <field name="notes" title="ملاحظات إضافية" type="textarea" rows="3"/>
        </fields>
        
        <save-action>
            INSERT INTO follows (
                case_id, service_id, user_id, report, date_field, status, notes
            ) VALUES (
                @case_id, @service_id, @current_user_id, @report, @date_field, @status, @notes
            )
        </save-action>
        
        <update-action>
            UPDATE follows SET
                case_id = @case_id,
                service_id = @service_id,
                report = @report,
                date_field = @date_field,
                status = @status,
                notes = @notes
            WHERE id = @id
        </update-action>
    </form-view>
</form-module>

<!-- =====================================================
     نموذج توزيع القضايا
     ===================================================== -->
<form-module name="case-distribution" title="توزيع القضايا" icon="share-alt">
    
    <list-view name="list" title="قائمة توزيع القضايا">
        <data-source>
            SELECT 
                cd.id,
                i.case_number,
                i.title as case_title,
                l.name as lineage_name,
                cd.distribution_date,
                COUNT(sd.id) as services_count,
                SUM(sd.amount) as total_amount,
                cd.created_date
            FROM case_distribution cd
            JOIN issues i ON cd.issue_id = i.id
            JOIN lineages l ON cd.lineage_id = l.id
            LEFT JOIN service_distributions sd ON cd.id = sd.case_distribution_id
            GROUP BY cd.id, i.case_number, i.title, l.name, cd.distribution_date, cd.created_date
            ORDER BY cd.distribution_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="case_title" title="عنوان القضية" width="200"/>
            <column name="lineage_name" title="النسبة المالية" width="150"/>
            <column name="distribution_date" title="تاريخ التوزيع" width="120" format="date"/>
            <column name="services_count" title="عدد الخدمات" width="100"/>
            <column name="total_amount" title="إجمالي المبلغ" width="120" format="currency"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <actions>
            <action name="add" title="توزيع قضية جديدة" icon="plus" color="primary"/>
            <action name="edit" title="تعديل التوزيع" icon="edit" color="warning"/>
            <action name="delete" title="حذف التوزيع" icon="delete" color="danger" confirm="true"/>
            <action name="view" title="عرض التفاصيل" icon="eye" color="info"/>
            <action name="services" title="إدارة الخدمات" icon="cogs" color="success"/>
        </actions>
    </list-view>
    
    <form-view name="add" title="توزيع قضية جديدة">
        <fields>
            <field name="issue_id" title="القضية" type="lookup" required="true"
                   lookup-table="issues" lookup-field="case_number + ' - ' + title"
                   lookup-filter="id NOT IN (SELECT issue_id FROM case_distribution)"/>
            <field name="lineage_id" title="النسبة المالية" type="lookup" required="true"
                   lookup-table="lineages" lookup-field="name"/>
            <field name="distribution_date" title="تاريخ التوزيع" type="date" default="today"/>
            <field name="notes" title="ملاحظات التوزيع" type="textarea" rows="3"/>
        </fields>
        
        <save-action>
            INSERT INTO case_distribution (
                issue_id, lineage_id, distribution_date, notes
            ) VALUES (
                @issue_id, @lineage_id, @distribution_date, @notes
            )
        </save-action>
    </form-view>
</form-module>

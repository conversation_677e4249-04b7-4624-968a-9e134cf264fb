/* ملف CSS بسيط للنظام المحاسبي */
body { font-family: Arial, sans-serif; direction: rtl; }
.table { width: 100%; border-collapse: collapse; }
.table th, .table td { padding: 8px; border: 1px solid #ddd; text-align: right; }
.table th { background-color: #f2f2f2; }
.btn { padding: 6px 12px; margin: 2px; border: none; border-radius: 4px; cursor: pointer; }
.btn-primary { background-color: #007bff; color: white; }
.btn-success { background-color: #28a745; color: white; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-warning { background-color: #ffc107; color: black; }
.form-control { width: 100%; padding: 6px; border: 1px solid #ccc; border-radius: 4px; }
.alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
.alert-success { background-color: #d4edda; color: #155724; }
.alert-danger { background-color: #f8d7da; color: #721c24; }
.container { max-width: 1200px; margin: 0 auto; padding: 15px; }

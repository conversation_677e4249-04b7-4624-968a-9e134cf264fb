<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج إدارة الخدمات القانونية - متوافق مع نظام RemoX -->
<form-module name="legal-services" title="إدارة الخدمات القانونية" icon="cogs" 
             permission="legal_settings_view" add-permission="legal_settings_edit" 
             edit-permission="legal_settings_edit" delete-permission="legal_settings_edit">

    <!-- قائمة الخدمات -->
    <list-view name="list" title="قائمة الخدمات القانونية" permission="legal_settings_view">
        <data-source>
            SELECT 
                id, 
                name, 
                description, 
                default_percentage,
                is_active, 
                created_date,
                (SELECT COUNT(*) FROM follows WHERE service_id = services.id) as follows_count,
                (SELECT COUNT(*) FROM follows WHERE service_id = services.id AND status = 'completed') as completed_follows,
                (SELECT COUNT(DISTINCT case_id) FROM follows WHERE service_id = services.id) as cases_count
            FROM services
            ORDER BY name
        </data-source>
        
        <columns>
            <column name="name" title="اسم الخدمة" width="200" sortable="true" searchable="true"/>
            <column name="description" title="الوصف" width="250" truncate="80" searchable="true"/>
            <column name="default_percentage" title="النسبة الافتراضية" width="120" format="percentage" align="center"/>
            <column name="follows_count" title="عدد المتابعات" width="100" format="number" align="center"/>
            <column name="completed_follows" title="المتابعات المكتملة" width="130" format="number" align="center"/>
            <column name="cases_count" title="عدد القضايا" width="100" format="number" align="center"/>
            <column name="is_active" title="نشط" width="80" format="boolean" align="center" filter="true"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <filters>
            <filter name="is_active" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="true">نشط</option>
                    <option value="false">غير نشط</option>
                </options>
            </filter>
            <filter name="has_follows" title="يحتوي على متابعات" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="yes">نعم</option>
                    <option value="no">لا</option>
                </options>
            </filter>
            <filter name="percentage_range" title="نطاق النسبة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="0-10">0% - 10%</option>
                    <option value="10-20">10% - 20%</option>
                    <option value="20-30">20% - 30%</option>
                    <option value="30+">أكثر من 30%</option>
                </options>
            </filter>
        </filters>
        
        <actions>
            <action name="add" title="إضافة خدمة جديدة" icon="plus" color="primary" 
                   permission="legal_settings_edit"/>
            <action name="edit" title="تعديل" icon="edit" color="warning" 
                   permission="legal_settings_edit"/>
            <action name="delete" title="حذف" icon="delete" color="danger" 
                   permission="legal_settings_edit" confirm="true"
                   confirm-message="هل أنت متأكد من حذف هذه الخدمة؟ سيؤثر على المتابعات المرتبطة بها."/>
            <action name="toggle_status" title="تغيير الحالة" icon="toggle-on" color="info" 
                   permission="legal_settings_edit"/>
            <action name="view_follows" title="عرض المتابعات" icon="calendar" color="success" 
                   url="/app/fms/?fm=case-follows&amp;service_id={id}"/>
        </actions>
        
        <summary>
            <field name="total_services" title="إجمالي الخدمات" type="count"/>
            <field name="active_services" title="الخدمات النشطة" type="count" condition="is_active = true"/>
            <field name="total_follows" title="إجمالي المتابعات" type="sum" column="follows_count"/>
            <field name="avg_percentage" title="متوسط النسبة" type="avg" column="default_percentage" format="percentage"/>
        </summary>
    </list-view>
    
    <!-- نموذج إضافة خدمة جديدة -->
    <form-view name="add" title="إضافة خدمة قانونية جديدة" permission="legal_settings_edit">
        <fields>
            <field name="name" title="اسم الخدمة" type="text" required="true" 
                   placeholder="مثال: استشارة قانونية" maxlength="100"
                   help="اسم واضح ومميز للخدمة القانونية"/>
            <field name="description" title="وصف الخدمة" type="textarea" rows="4" 
                   placeholder="وصف تفصيلي للخدمة" maxlength="500"
                   help="وصف يوضح طبيعة هذه الخدمة ومتى تستخدم"/>
            <field name="default_percentage" title="النسبة الافتراضية %" type="decimal" 
                   placeholder="0.00" min="0" max="100" step="0.01"
                   help="النسبة المالية الافتراضية لهذه الخدمة"/>
            <field name="is_active" title="نشط" type="checkbox" default="true"
                   help="تحديد ما إذا كانت هذه الخدمة متاحة للاستخدام"/>
        </fields>
        
        <save-action>
            INSERT INTO services (name, description, default_percentage, is_active, created_date)
            VALUES (@name, @description, @default_percentage, @is_active, CURRENT_TIMESTAMP)
        </save-action>
        
        <validation>
            <rule field="name" type="required" message="اسم الخدمة مطلوب"/>
            <rule field="name" type="unique" table="services" message="اسم الخدمة موجود مسبقاً"/>
            <rule field="name" type="minlength" value="3" message="اسم الخدمة يجب أن يكون 3 أحرف على الأقل"/>
            <rule field="default_percentage" type="numeric" min="0" max="100" message="النسبة يجب أن تكون بين 0 و 100"/>
        </validation>
    </form-view>
    
    <!-- نموذج تعديل الخدمة -->
    <form-view name="edit" title="تعديل الخدمة القانونية" permission="legal_settings_edit">
        <load-action>
            SELECT 
                *,
                (SELECT COUNT(*) FROM follows WHERE service_id = services.id) as follows_count,
                (SELECT COUNT(DISTINCT case_id) FROM follows WHERE service_id = services.id) as cases_count
            FROM services 
            WHERE id = @id
        </load-action>
        
        <fields>
            <field name="name" title="اسم الخدمة" type="text" required="true" maxlength="100"/>
            <field name="description" title="وصف الخدمة" type="textarea" rows="4" maxlength="500"/>
            <field name="default_percentage" title="النسبة الافتراضية %" type="decimal" 
                   min="0" max="100" step="0.01"/>
            <field name="is_active" title="نشط" type="checkbox"/>
            <field name="follows_count" title="عدد المتابعات المرتبطة" type="number" readonly="true"
                   help="عدد المتابعات التي تستخدم هذه الخدمة"/>
            <field name="cases_count" title="عدد القضايا المرتبطة" type="number" readonly="true"
                   help="عدد القضايا التي تحتوي على متابعات بهذه الخدمة"/>
        </fields>
        
        <update-action>
            UPDATE services SET
                name = @name,
                description = @description,
                default_percentage = @default_percentage,
                is_active = @is_active,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = @id
        </update-action>
        
        <validation>
            <rule field="name" type="required" message="اسم الخدمة مطلوب"/>
            <rule field="name" type="unique" table="services" exclude-id="@id" message="اسم الخدمة موجود مسبقاً"/>
            <rule field="default_percentage" type="numeric" min="0" max="100" message="النسبة يجب أن تكون بين 0 و 100"/>
        </validation>
    </form-view>
    
    <!-- نموذج عرض تفاصيل الخدمة -->
    <form-view name="view" title="تفاصيل الخدمة القانونية" permission="legal_settings_view" readonly="true">
        <load-action>
            SELECT 
                s.*,
                (SELECT COUNT(*) FROM follows WHERE service_id = s.id) as total_follows,
                (SELECT COUNT(*) FROM follows WHERE service_id = s.id AND status = 'completed') as completed_follows,
                (SELECT COUNT(*) FROM follows WHERE service_id = s.id AND status = 'pending') as pending_follows,
                (SELECT COUNT(DISTINCT case_id) FROM follows WHERE service_id = s.id) as total_cases,
                (SELECT COUNT(DISTINCT f.case_id) FROM follows f JOIN issues i ON f.case_id = i.id WHERE f.service_id = s.id AND i.status = 'active') as active_cases,
                (SELECT AVG(i.amount) FROM follows f JOIN issues i ON f.case_id = i.id WHERE f.service_id = s.id) as avg_case_amount
            FROM services s
            WHERE s.id = @id
        </load-action>
        
        <tabs>
            <tab name="details" title="تفاصيل الخدمة" icon="info">
                <fields>
                    <field name="name" title="اسم الخدمة" type="text" readonly="true"/>
                    <field name="description" title="الوصف" type="textarea" readonly="true"/>
                    <field name="default_percentage" title="النسبة الافتراضية" type="decimal" format="percentage" readonly="true"/>
                    <field name="is_active" title="نشط" type="checkbox" readonly="true"/>
                    <field name="created_date" title="تاريخ الإنشاء" type="datetime" readonly="true"/>
                    <field name="updated_at" title="آخر تحديث" type="datetime" readonly="true"/>
                </fields>
            </tab>
            
            <tab name="statistics" title="الإحصائيات" icon="chart">
                <fields>
                    <field name="total_follows" title="إجمالي المتابعات" type="number" readonly="true"/>
                    <field name="completed_follows" title="المتابعات المكتملة" type="number" readonly="true"/>
                    <field name="pending_follows" title="المتابعات المعلقة" type="number" readonly="true"/>
                    <field name="total_cases" title="إجمالي القضايا" type="number" readonly="true"/>
                    <field name="active_cases" title="القضايا النشطة" type="number" readonly="true"/>
                    <field name="avg_case_amount" title="متوسط مبلغ القضايا" type="decimal" format="currency" readonly="true"/>
                </fields>
                
                <actions>
                    <action name="view_follows" title="عرض جميع المتابعات" icon="calendar" color="primary" 
                           url="/app/fms/?fm=case-follows&amp;service_id={id}"/>
                    <action name="view_completed_follows" title="عرض المتابعات المكتملة" icon="check" color="success" 
                           url="/app/fms/?fm=case-follows&amp;service_id={id}&amp;status=completed"/>
                    <action name="view_pending_follows" title="عرض المتابعات المعلقة" icon="clock" color="warning" 
                           url="/app/fms/?fm=case-follows&amp;service_id={id}&amp;status=pending"/>
                </actions>
            </tab>
        </tabs>
    </form-view>
</form-module>

<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج المحاكم - متوافق مع نظام RemoX -->
<form-module name="courts" title="المحاكم" icon="university"
             permission="legal_settings_view" add-permission="legal_settings_edit"
             edit-permission="legal_settings_edit" delete-permission="legal_settings_edit">

    <!-- قائمة المحاكم -->
    <list-view name="list" title="قائمة المحاكم" permission="legal_settings_view">
        <data-source>
            SELECT 
                id, 
                name, 
                type,
                governorate_name,
                address,
                phone,
                is_active, 
                created_date,
                (SELECT COUNT(*) FROM issues WHERE court_id = courts.id) as cases_count,
                (SELECT COUNT(*) FROM issues WHERE court_id = courts.id AND status = 'active') as active_cases_count
            FROM courts
            ORDER BY governorate_name, name
        </data-source>

        <columns>
            <column name="name" title="اسم المحكمة" width="200" sortable="true"/>
            <column name="type" title="نوع المحكمة" width="120" sortable="true"/>
            <column name="governorate_name" title="المحافظة" width="120" sortable="true"/>
            <column name="address" title="العنوان" width="200"/>
            <column name="phone" title="الهاتف" width="120"/>
            <column name="cases_count" title="عدد القضايا" width="80" align="center"/>
            <column name="active_cases_count" title="القضايا النشطة" width="80" align="center"/>
            <column name="is_active" title="نشط" width="60" type="boolean"/>
            <column name="created_date" title="تاريخ الإنشاء" width="100" type="date"/>
        </columns>

        <filters>
            <filter name="name" title="اسم المحكمة" type="text" field="name"/>
            <filter name="type" title="نوع المحكمة" type="select" field="type">
                <options>
                    <option value="">الكل</option>
                    <option value="ابتدائية">ابتدائية</option>
                    <option value="استئناف">استئناف</option>
                    <option value="تمييز">تمييز</option>
                    <option value="إدارية">إدارية</option>
                    <option value="جنائية">جنائية</option>
                    <option value="مدنية">مدنية</option>
                    <option value="تجارية">تجارية</option>
                    <option value="عمالية">عمالية</option>
                </options>
            </filter>
            <filter name="governorate_name" title="المحافظة" type="text" field="governorate_name"/>
            <filter name="is_active" title="الحالة" type="select" field="is_active">
                <options>
                    <option value="">الكل</option>
                    <option value="true">نشط</option>
                    <option value="false">غير نشط</option>
                </options>
            </filter>
        </filters>

        <actions>
            <action name="add" title="إضافة محكمة جديدة" icon="plus" color="primary"
                   permission="legal_settings_edit"/>
            <action name="edit" title="تعديل" icon="edit" color="warning"
                   permission="legal_settings_edit"/>
            <action name="delete" title="حذف" icon="delete" color="danger"
                   permission="legal_settings_edit" confirm="true"/>
            <action name="view" title="عرض التفاصيل" icon="eye" color="info"/>
            <action name="cases" title="قضايا المحكمة" icon="gavel" color="success"
                   url="/app/fms/?fm=legal-issues&amp;cmd=list&amp;court_id={id}"/>
        </actions>

        <summary>
            <field name="total_courts" title="إجمالي المحاكم" type="count"/>
            <field name="active_courts" title="المحاكم النشطة" type="count" filter="is_active = true"/>
            <field name="total_cases" title="إجمالي القضايا" type="sum" field="cases_count"/>
        </summary>
    </list-view>

    <!-- نموذج إضافة محكمة جديدة -->
    <form-view name="add" title="إضافة محكمة جديدة" permission="legal_settings_edit">
        <fields>
            <field name="name" title="اسم المحكمة" type="text" required="true" maxlength="200"
                   placeholder="مثال: محكمة بداءة بغداد"
                   help="اسم المحكمة كاملاً"/>
            <field name="type" title="نوع المحكمة" type="select" required="true">
                <options>
                    <option value="ابتدائية">ابتدائية</option>
                    <option value="استئناف">استئناف</option>
                    <option value="تمييز">تمييز</option>
                    <option value="إدارية">إدارية</option>
                    <option value="جنائية">جنائية</option>
                    <option value="مدنية">مدنية</option>
                    <option value="تجارية">تجارية</option>
                    <option value="عمالية">عمالية</option>
                </options>
            </field>
            <field name="governorate_id" title="المحافظة" type="lookup" required="true"
                   lookup-table="governorates" lookup-field="name" lookup-value="id"
                   lookup-filter="is_active = true"
                   help="اختر المحافظة التي تقع فيها المحكمة"/>
            <field name="address" title="العنوان" type="textarea" rows="3"
                   placeholder="العنوان التفصيلي للمحكمة"/>
            <field name="phone" title="الهاتف" type="text" maxlength="50"
                   placeholder="رقم الهاتف أو الهواتف"/>
            <field name="email" title="البريد الإلكتروني" type="email" maxlength="100"/>
            <field name="website" title="الموقع الإلكتروني" type="url" maxlength="200"/>
            <field name="working_hours" title="ساعات العمل" type="text" maxlength="100"
                   placeholder="مثال: 8:00 ص - 2:00 م"/>
            <field name="notes" title="ملاحظات" type="textarea" rows="3"/>
            <field name="is_active" title="نشط" type="checkbox" default="true"/>
        </fields>

        <save-action>
            INSERT INTO courts (
                name, type, governorate_id, governorate_name, address, phone, email, 
                website, working_hours, notes, is_active, created_date
            ) VALUES (
                @name, @type, @governorate_id, 
                (SELECT name FROM governorates WHERE id = @governorate_id),
                @address, @phone, @email, @website, @working_hours, @notes, 
                @is_active, CURRENT_TIMESTAMP
            )
        </save-action>

        <validation>
            <rule field="name" type="required" message="اسم المحكمة مطلوب"/>
            <rule field="name" type="unique" table="courts" message="اسم المحكمة موجود مسبقاً"/>
            <rule field="type" type="required" message="نوع المحكمة مطلوب"/>
            <rule field="governorate_id" type="required" message="المحافظة مطلوبة"/>
        </validation>
    </form-view>

    <!-- نموذج تعديل محكمة -->
    <form-view name="edit" title="تعديل بيانات المحكمة" permission="legal_settings_edit">
        <load-action>
            SELECT * FROM courts WHERE id = @id
        </load-action>

        <fields>
            <field name="name" title="اسم المحكمة" type="text" required="true" maxlength="200"/>
            <field name="type" title="نوع المحكمة" type="select" required="true">
                <options>
                    <option value="ابتدائية">ابتدائية</option>
                    <option value="استئناف">استئناف</option>
                    <option value="تمييز">تمييز</option>
                    <option value="إدارية">إدارية</option>
                    <option value="جنائية">جنائية</option>
                    <option value="مدنية">مدنية</option>
                    <option value="تجارية">تجارية</option>
                    <option value="عمالية">عمالية</option>
                </options>
            </field>
            <field name="governorate_id" title="المحافظة" type="lookup" required="true"
                   lookup-table="governorates" lookup-field="name" lookup-value="id"/>
            <field name="address" title="العنوان" type="textarea" rows="3"/>
            <field name="phone" title="الهاتف" type="text" maxlength="50"/>
            <field name="email" title="البريد الإلكتروني" type="email" maxlength="100"/>
            <field name="website" title="الموقع الإلكتروني" type="url" maxlength="200"/>
            <field name="working_hours" title="ساعات العمل" type="text" maxlength="100"/>
            <field name="notes" title="ملاحظات" type="textarea" rows="3"/>
            <field name="is_active" title="نشط" type="checkbox"/>
        </fields>

        <save-action>
            UPDATE courts SET 
                name = @name, 
                type = @type, 
                governorate_id = @governorate_id,
                governorate_name = (SELECT name FROM governorates WHERE id = @governorate_id),
                address = @address, 
                phone = @phone, 
                email = @email,
                website = @website,
                working_hours = @working_hours,
                notes = @notes, 
                is_active = @is_active,
                updated_date = CURRENT_TIMESTAMP
            WHERE id = @id
        </save-action>

        <validation>
            <rule field="name" type="required" message="اسم المحكمة مطلوب"/>
            <rule field="type" type="required" message="نوع المحكمة مطلوب"/>
            <rule field="governorate_id" type="required" message="المحافظة مطلوبة"/>
        </validation>
    </form-view>

    <!-- نموذج عرض تفاصيل المحكمة -->
    <form-view name="view" title="تفاصيل المحكمة" permission="legal_settings_view" readonly="true">
        <load-action>
            SELECT 
                c.*,
                g.name as governorate_display,
                (SELECT COUNT(*) FROM issues WHERE court_id = c.id) as total_cases,
                (SELECT COUNT(*) FROM issues WHERE court_id = c.id AND status = 'active') as active_cases,
                (SELECT COUNT(*) FROM issues WHERE court_id = c.id AND status = 'closed') as closed_cases
            FROM courts c
            LEFT JOIN governorates g ON c.governorate_id = g.id
            WHERE c.id = @id
        </load-action>

        <fields>
            <field name="name" title="اسم المحكمة" type="text" readonly="true"/>
            <field name="type" title="نوع المحكمة" type="text" readonly="true"/>
            <field name="governorate_display" title="المحافظة" type="text" readonly="true"/>
            <field name="address" title="العنوان" type="textarea" readonly="true"/>
            <field name="phone" title="الهاتف" type="text" readonly="true"/>
            <field name="email" title="البريد الإلكتروني" type="text" readonly="true"/>
            <field name="website" title="الموقع الإلكتروني" type="text" readonly="true"/>
            <field name="working_hours" title="ساعات العمل" type="text" readonly="true"/>
            <field name="total_cases" title="إجمالي القضايا" type="number" readonly="true"/>
            <field name="active_cases" title="القضايا النشطة" type="number" readonly="true"/>
            <field name="closed_cases" title="القضايا المغلقة" type="number" readonly="true"/>
            <field name="notes" title="ملاحظات" type="textarea" readonly="true"/>
            <field name="is_active" title="نشط" type="checkbox" readonly="true"/>
            <field name="created_date" title="تاريخ الإنشاء" type="datetime" readonly="true"/>
        </fields>
    </form-view>

</form-module>

# نظام mohhash - نسخة من RemoX

## معلومات النظام
- **اسم النظام**: mohhash
- **المنفذ**: 8081
- **الرابط**: http://localhost:8081
- **قاعدة البيانات**: HsAppsDb_Shared_mohhash

## المجلدات
- `App/` - ملفات التطبيق
- `Data/` - قاعدة البيانات والبيانات
- `Backup/` - النسخ الاحتياطية

## التشغيل
1. تشغيل الملف: `start_mohhash.bat`
2. أو فتح الرابط مباشرة: http://localhost:8081

## ملاحظات
- هذا النظام نسخة كاملة من النظام الأصلي RemoX
- جميع البيانات والإعدادات منسوخة
- يعمل بشكل مستقل عن النظام الأصلي
- يستخدم قاعدة بيانات منفصلة

## معلومات تقنية
- **Framework**: ASP.NET 4.8
- **Database**: SQL Server (SQLREMOX Instance)
- **Port**: 8081
- **Physical Path**: D:\RemoX\mohhash\App\bin\webapp

تم إنشاء هذا النظام بواسطة Augment Agent

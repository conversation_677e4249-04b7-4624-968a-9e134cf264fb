-- ربط النظام القضائي بالنظام المحاسبي في mohhash
-- تاريخ الإنشاء: 2025-08-21
-- الهدف: إنشاء ربط تلقائي بين القضايا والحسابات المحاسبية

-- =====================================================
-- إنشاء حسابات رئيسية للنظام القضائي
-- =====================================================

-- إنشاء حساب رئيسي للقضايا في دليل الحسابات
INSERT INTO chart_of_accounts (
    account_code, account_name, account_type, account_level,
    is_main_account, is_active, description
) VALUES (
    '4000', 'إيرادات القضايا', 'revenue', 1,
    true, true, 'الحساب الرئيسي لجميع إيرادات القضايا القانونية'
) ON CONFLICT (account_code) DO NOTHING;

-- إنشاء حساب رئيسي لمصروفات القضايا
INSERT INTO chart_of_accounts (
    account_code, account_name, account_type, account_level,
    is_main_account, is_active, description
) VALUES (
    '5000', 'مصروفات القضايا', 'expense', 1,
    true, true, 'الحساب الرئيسي لجميع مصروفات القضايا القانونية'
) ON CONFLICT (account_code) DO NOTHING;

-- إنشاء حساب فرعي لأتعاب المحامين
INSERT INTO chart_of_accounts (
    account_code, account_name, account_type, account_level,
    parent_id, is_sub_account, is_active, description
) VALUES (
    '4100', 'أتعاب المحامين', 'revenue', 2,
    (SELECT id FROM chart_of_accounts WHERE account_code = '4000'),
    true, true, 'إيرادات أتعاب المحامين من القضايا'
) ON CONFLICT (account_code) DO NOTHING;

-- إنشاء حساب فرعي لرسوم المحاكم
INSERT INTO chart_of_accounts (
    account_code, account_name, account_type, account_level,
    parent_id, is_sub_account, is_active, description
) VALUES (
    '5100', 'رسوم المحاكم', 'expense', 2,
    (SELECT id FROM chart_of_accounts WHERE account_code = '5000'),
    true, true, 'رسوم ومصروفات المحاكم والإجراءات القانونية'
) ON CONFLICT (account_code) DO NOTHING;

-- =====================================================
-- إنشاء دوال الربط التلقائي
-- =====================================================

-- دالة إنشاء حساب تلقائي لكل قضية
CREATE OR REPLACE FUNCTION create_case_account()
RETURNS TRIGGER AS $$
DECLARE
    main_revenue_account_id INTEGER;
    main_expense_account_id INTEGER;
    case_revenue_account_id INTEGER;
    case_expense_account_id INTEGER;
BEGIN
    -- الحصول على معرف حساب الإيرادات الرئيسي
    SELECT id INTO main_revenue_account_id 
    FROM chart_of_accounts 
    WHERE account_code = '4100' 
    LIMIT 1;
    
    -- الحصول على معرف حساب المصروفات الرئيسي
    SELECT id INTO main_expense_account_id 
    FROM chart_of_accounts 
    WHERE account_code = '5100' 
    LIMIT 1;
    
    -- إنشاء حساب إيرادات للقضية
    INSERT INTO chart_of_accounts (
        account_code,
        account_name,
        account_type,
        account_level,
        parent_id,
        is_sub_account,
        is_active,
        description
    ) VALUES (
        '4100-' || NEW.case_number,
        'إيرادات قضية ' || NEW.case_number || ' - ' || NEW.title,
        'revenue',
        3,
        main_revenue_account_id,
        true,
        true,
        'حساب إيرادات القضية رقم ' || NEW.case_number
    ) RETURNING id INTO case_revenue_account_id;
    
    -- إنشاء حساب مصروفات للقضية
    INSERT INTO chart_of_accounts (
        account_code,
        account_name,
        account_type,
        account_level,
        parent_id,
        is_sub_account,
        is_active,
        description
    ) VALUES (
        '5100-' || NEW.case_number,
        'مصروفات قضية ' || NEW.case_number || ' - ' || NEW.title,
        'expense',
        3,
        main_expense_account_id,
        true,
        true,
        'حساب مصروفات القضية رقم ' || NEW.case_number
    ) RETURNING id INTO case_expense_account_id;
    
    -- ربط القضية بحساب الإيرادات
    NEW.account_id := case_revenue_account_id;
    
    -- إنشاء سجل في جدول ربط الحسابات
    INSERT INTO account_case_links (
        case_id, revenue_account_id, expense_account_id, created_date
    ) VALUES (
        NEW.id, case_revenue_account_id, case_expense_account_id, CURRENT_TIMESTAMP
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- إنشاء جدول ربط الحسابات بالقضايا
-- =====================================================

CREATE TABLE IF NOT EXISTS account_case_links (
    id SERIAL PRIMARY KEY,
    case_id INTEGER REFERENCES issues(id),
    revenue_account_id INTEGER REFERENCES chart_of_accounts(id),
    expense_account_id INTEGER REFERENCES chart_of_accounts(id),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- إنشاء محفزات الربط التلقائي
-- =====================================================

-- محفز إنشاء الحسابات عند إضافة قضية جديدة
DROP TRIGGER IF EXISTS trigger_create_case_account ON issues;
CREATE TRIGGER trigger_create_case_account
    BEFORE INSERT ON issues
    FOR EACH ROW
    EXECUTE FUNCTION create_case_account();

-- =====================================================
-- دوال إنشاء القيود المحاسبية التلقائية
-- =====================================================

-- دالة إنشاء قيد محاسبي عند استلام أتعاب
CREATE OR REPLACE FUNCTION create_fee_journal_entry(
    p_case_id INTEGER,
    p_amount DECIMAL(12,2),
    p_description TEXT,
    p_payment_method VARCHAR(50) DEFAULT 'نقدي'
)
RETURNS INTEGER AS $$
DECLARE
    v_journal_entry_id INTEGER;
    v_revenue_account_id INTEGER;
    v_cash_account_id INTEGER;
    v_entry_number VARCHAR(50);
BEGIN
    -- إنشاء رقم القيد
    v_entry_number := 'JE-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || 
                      LPAD(NEXTVAL('journal_entry_seq')::TEXT, 4, '0');
    
    -- الحصول على حساب إيرادات القضية
    SELECT revenue_account_id INTO v_revenue_account_id
    FROM account_case_links
    WHERE case_id = p_case_id;
    
    -- الحصول على حساب النقدية
    SELECT id INTO v_cash_account_id
    FROM chart_of_accounts
    WHERE account_code = '1100' -- حساب النقدية
    LIMIT 1;
    
    -- إنشاء القيد الرئيسي
    INSERT INTO journal_entries (
        entry_number, description, date, total_debit, total_credit, status
    ) VALUES (
        v_entry_number, p_description, CURRENT_DATE, p_amount, p_amount, 'approved'
    ) RETURNING id INTO v_journal_entry_id;
    
    -- إضافة تفاصيل القيد - الجانب المدين (النقدية)
    INSERT INTO journal_entry_details (
        journal_entry_id, account_id, debit_amount, credit_amount, description
    ) VALUES (
        v_journal_entry_id, v_cash_account_id, p_amount, 0, 
        'استلام أتعاب - ' || p_description
    );
    
    -- إضافة تفاصيل القيد - الجانب الدائن (الإيرادات)
    INSERT INTO journal_entry_details (
        journal_entry_id, account_id, debit_amount, credit_amount, description
    ) VALUES (
        v_journal_entry_id, v_revenue_account_id, 0, p_amount, 
        'إيرادات أتعاب - ' || p_description
    );
    
    RETURN v_journal_entry_id;
END;
$$ LANGUAGE plpgsql;

-- دالة إنشاء قيد محاسبي عند دفع مصروفات
CREATE OR REPLACE FUNCTION create_expense_journal_entry(
    p_case_id INTEGER,
    p_amount DECIMAL(12,2),
    p_description TEXT,
    p_expense_type VARCHAR(100) DEFAULT 'مصروفات عامة'
)
RETURNS INTEGER AS $$
DECLARE
    v_journal_entry_id INTEGER;
    v_expense_account_id INTEGER;
    v_cash_account_id INTEGER;
    v_entry_number VARCHAR(50);
BEGIN
    -- إنشاء رقم القيد
    v_entry_number := 'JE-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || 
                      LPAD(NEXTVAL('journal_entry_seq')::TEXT, 4, '0');
    
    -- الحصول على حساب مصروفات القضية
    SELECT expense_account_id INTO v_expense_account_id
    FROM account_case_links
    WHERE case_id = p_case_id;
    
    -- الحصول على حساب النقدية
    SELECT id INTO v_cash_account_id
    FROM chart_of_accounts
    WHERE account_code = '1100' -- حساب النقدية
    LIMIT 1;
    
    -- إنشاء القيد الرئيسي
    INSERT INTO journal_entries (
        entry_number, description, date, total_debit, total_credit, status
    ) VALUES (
        v_entry_number, p_description, CURRENT_DATE, p_amount, p_amount, 'approved'
    ) RETURNING id INTO v_journal_entry_id;
    
    -- إضافة تفاصيل القيد - الجانب المدين (المصروفات)
    INSERT INTO journal_entry_details (
        journal_entry_id, account_id, debit_amount, credit_amount, description
    ) VALUES (
        v_journal_entry_id, v_expense_account_id, p_amount, 0, 
        'مصروفات قضية - ' || p_description
    );
    
    -- إضافة تفاصيل القيد - الجانب الدائن (النقدية)
    INSERT INTO journal_entry_details (
        journal_entry_id, account_id, debit_amount, credit_amount, description
    ) VALUES (
        v_journal_entry_id, v_cash_account_id, 0, p_amount, 
        'دفع مصروفات - ' || p_description
    );
    
    RETURN v_journal_entry_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- إنشاء views للتقارير المالية للقضايا
-- =====================================================

-- view لملخص القضايا المالي
CREATE OR REPLACE VIEW cases_financial_summary AS
SELECT 
    i.id,
    i.case_number,
    i.title,
    i.client_name,
    i.amount as contract_amount,
    COALESCE(revenues.total_revenue, 0) as total_revenue,
    COALESCE(expenses.total_expenses, 0) as total_expenses,
    (COALESCE(revenues.total_revenue, 0) - COALESCE(expenses.total_expenses, 0)) as net_profit,
    CASE 
        WHEN i.amount > 0 THEN 
            ROUND((COALESCE(revenues.total_revenue, 0) / i.amount) * 100, 2)
        ELSE 0 
    END as collection_percentage
FROM issues i
LEFT JOIN account_case_links acl ON i.id = acl.case_id
LEFT JOIN (
    SELECT 
        acl.case_id,
        SUM(jed.credit_amount) as total_revenue
    FROM account_case_links acl
    JOIN journal_entry_details jed ON acl.revenue_account_id = jed.account_id
    JOIN journal_entries je ON jed.journal_entry_id = je.id
    WHERE je.status = 'approved'
    GROUP BY acl.case_id
) revenues ON i.id = revenues.case_id
LEFT JOIN (
    SELECT 
        acl.case_id,
        SUM(jed.debit_amount) as total_expenses
    FROM account_case_links acl
    JOIN journal_entry_details jed ON acl.expense_account_id = jed.account_id
    JOIN journal_entries je ON jed.journal_entry_id = je.id
    WHERE je.status = 'approved'
    GROUP BY acl.case_id
) expenses ON i.id = expenses.case_id;

-- =====================================================
-- إنشاء إجراءات مخزنة للتقارير
-- =====================================================

-- إجراء لإنشاء تقرير الربحية حسب المحامي
CREATE OR REPLACE FUNCTION get_lawyer_profitability_report(
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    lawyer_id INTEGER,
    lawyer_name VARCHAR(255),
    total_cases INTEGER,
    total_revenue DECIMAL(12,2),
    total_expenses DECIMAL(12,2),
    net_profit DECIMAL(12,2),
    average_case_value DECIMAL(12,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sd.lawyer_id,
        e.name as lawyer_name,
        COUNT(DISTINCT cd.issue_id)::INTEGER as total_cases,
        COALESCE(SUM(revenues.total_revenue), 0) as total_revenue,
        COALESCE(SUM(expenses.total_expenses), 0) as total_expenses,
        (COALESCE(SUM(revenues.total_revenue), 0) - COALESCE(SUM(expenses.total_expenses), 0)) as net_profit,
        CASE 
            WHEN COUNT(DISTINCT cd.issue_id) > 0 THEN
                COALESCE(SUM(revenues.total_revenue), 0) / COUNT(DISTINCT cd.issue_id)
            ELSE 0 
        END as average_case_value
    FROM service_distributions sd
    JOIN case_distribution cd ON sd.case_distribution_id = cd.id
    JOIN issues i ON cd.issue_id = i.id
    JOIN employees e ON sd.lawyer_id = e.id
    LEFT JOIN account_case_links acl ON i.id = acl.case_id
    LEFT JOIN (
        SELECT 
            acl.case_id,
            SUM(jed.credit_amount) as total_revenue
        FROM account_case_links acl
        JOIN journal_entry_details jed ON acl.revenue_account_id = jed.account_id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE je.status = 'approved'
          AND (p_start_date IS NULL OR je.date >= p_start_date)
          AND (p_end_date IS NULL OR je.date <= p_end_date)
        GROUP BY acl.case_id
    ) revenues ON i.id = revenues.case_id
    LEFT JOIN (
        SELECT 
            acl.case_id,
            SUM(jed.debit_amount) as total_expenses
        FROM account_case_links acl
        JOIN journal_entry_details jed ON acl.expense_account_id = jed.account_id
        JOIN journal_entries je ON jed.journal_entry_id = je.id
        WHERE je.status = 'approved'
          AND (p_start_date IS NULL OR je.date >= p_start_date)
          AND (p_end_date IS NULL OR je.date <= p_end_date)
        GROUP BY acl.case_id
    ) expenses ON i.id = expenses.case_id
    WHERE (p_start_date IS NULL OR cd.distribution_date >= p_start_date)
      AND (p_end_date IS NULL OR cd.distribution_date <= p_end_date)
    GROUP BY sd.lawyer_id, e.name
    ORDER BY net_profit DESC;
END;
$$ LANGUAGE plpgsql;

<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج التقارير القانونية - متوافق مع نظام RemoX -->
<form-module name="legal-reports" title="التقارير القانونية" icon="chart-bar" 
             permission="legal_reports_view">

    <!-- تقرير ملخص القضايا -->
    <report-view name="cases-summary" title="ملخص القضايا" permission="legal_reports_view">
        <parameters>
            <parameter name="date_from" title="من تاريخ" type="date" default="first_day_of_month"/>
            <parameter name="date_to" title="إلى تاريخ" type="date" default="today"/>
            <parameter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="active">نشطة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </parameter>
            <parameter name="issue_type_id" title="نوع القضية" type="lookup" 
                      lookup-table="issue_types" lookup-field="name" lookup-value="id"/>
            <parameter name="court_id" title="المحكمة" type="lookup" 
                      lookup-table="courts" lookup-field="name" lookup-value="id"/>
            <parameter name="contract_method" title="طريقة التعاقد" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="بالجلسة">بالجلسة</option>
                    <option value="مقطوع">مقطوع</option>
                    <option value="نسبة">نسبة من المبلغ</option>
                    <option value="مختلط">مختلط</option>
                </options>
            </parameter>
        </parameters>
        
        <data-source>
            SELECT 
                i.id,
                i.case_number,
                i.title,
                i.client_name,
                COALESCE(it.name, i.issue_type) as issue_type,
                COALESCE(c.name, i.court_name) as court_name,
                i.status,
                i.amount,
                i.contract_method,
                COUNT(f.id) as follows_count,
                SUM(CASE WHEN f.status = 'completed' THEN 1 ELSE 0 END) as completed_follows,
                SUM(CASE WHEN f.status = 'pending' THEN 1 ELSE 0 END) as pending_follows,
                i.next_hearing,
                i.created_date
            FROM issues i
            LEFT JOIN issue_types it ON i.issue_type_id = it.id
            LEFT JOIN courts c ON i.court_id = c.id
            LEFT JOIN follows f ON i.id = f.case_id
            WHERE (@date_from IS NULL OR i.created_date >= @date_from)
              AND (@date_to IS NULL OR i.created_date <= @date_to)
              AND (@status = '' OR i.status = @status)
              AND (@issue_type_id IS NULL OR i.issue_type_id = @issue_type_id)
              AND (@court_id IS NULL OR i.court_id = @court_id)
              AND (@contract_method = '' OR i.contract_method = @contract_method)
            GROUP BY i.id, i.case_number, i.title, i.client_name, it.name, i.issue_type, 
                     c.name, i.court_name, i.status, i.amount, i.contract_method, 
                     i.next_hearing, i.created_date
            ORDER BY i.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120" searchable="true"/>
            <column name="title" title="العنوان" width="200" searchable="true"/>
            <column name="client_name" title="الموكل" width="150" searchable="true"/>
            <column name="issue_type" title="النوع" width="120"/>
            <column name="court_name" title="المحكمة" width="150"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="amount" title="المبلغ" width="120" format="currency"/>
            <column name="contract_method" title="طريقة التعاقد" width="100"/>
            <column name="follows_count" title="المتابعات" width="80" format="number"/>
            <column name="completed_follows" title="المكتملة" width="80" format="number"/>
            <column name="pending_follows" title="المعلقة" width="80" format="number"/>
            <column name="next_hearing" title="الجلسة القادمة" width="120" format="date"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <summary>
            <field name="total_cases" title="إجمالي القضايا" type="count"/>
            <field name="total_amount" title="إجمالي المبالغ" type="sum" column="amount" format="currency"/>
            <field name="avg_amount" title="متوسط المبلغ" type="avg" column="amount" format="currency"/>
            <field name="total_follows" title="إجمالي المتابعات" type="sum" column="follows_count"/>
            <field name="avg_follows_per_case" title="متوسط المتابعات لكل قضية" type="avg" column="follows_count"/>
            <field name="active_cases" title="القضايا النشطة" type="count" condition="status = 'active'"/>
            <field name="completed_cases" title="القضايا المكتملة" type="count" condition="status = 'completed'"/>
        </summary>
        
        <charts>
            <chart name="cases_by_status" title="القضايا حسب الحالة" type="pie" 
                   group-by="status" value="count"/>
            <chart name="cases_by_type" title="القضايا حسب النوع" type="bar" 
                   group-by="issue_type" value="count"/>
            <chart name="amount_by_month" title="المبالغ حسب الشهر" type="line" 
                   group-by="MONTH(created_date)" value="sum(amount)"/>
        </charts>
    </report-view>
    
    <!-- تقرير المتابعات -->
    <report-view name="follows-report" title="تقرير المتابعات" permission="legal_reports_view">
        <parameters>
            <parameter name="date_from" title="من تاريخ" type="date" default="first_day_of_month"/>
            <parameter name="date_to" title="إلى تاريخ" type="date" default="today"/>
            <parameter name="case_id" title="القضية" type="lookup" 
                      lookup-table="issues" lookup-field="case_number + ' - ' + title" lookup-value="id"/>
            <parameter name="service_id" title="نوع الخدمة" type="lookup" 
                      lookup-table="services" lookup-field="name" lookup-value="id"/>
            <parameter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </parameter>
            <parameter name="user_id" title="المستخدم" type="lookup" 
                      lookup-table="employees" lookup-field="name" lookup-value="id"/>
        </parameters>
        
        <data-source>
            SELECT 
                f.id,
                i.case_number,
                i.title as case_title,
                i.client_name,
                COALESCE(s.name, 'خدمة غير محددة') as service_name,
                f.report,
                f.date_field,
                f.status,
                COALESCE(e.name, u.username, 'غير محدد') as user_name,
                f.created_date,
                i.amount as case_amount
            FROM follows f
            JOIN issues i ON f.case_id = i.id
            LEFT JOIN services s ON f.service_id = s.id
            LEFT JOIN employees e ON f.user_id = e.id
            LEFT JOIN users u ON f.user_id = u.id
            WHERE (@date_from IS NULL OR f.date_field >= @date_from)
              AND (@date_to IS NULL OR f.date_field <= @date_to)
              AND (@case_id IS NULL OR f.case_id = @case_id)
              AND (@service_id IS NULL OR f.service_id = @service_id)
              AND (@status = '' OR f.status = @status)
              AND (@user_id IS NULL OR f.user_id = @user_id)
            ORDER BY f.date_field DESC, f.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="case_title" title="عنوان القضية" width="200"/>
            <column name="client_name" title="الموكل" width="120"/>
            <column name="service_name" title="نوع الخدمة" width="150"/>
            <column name="report" title="التقرير" width="300" truncate="100"/>
            <column name="date_field" title="تاريخ المتابعة" width="120" format="date"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="user_name" title="المستخدم" width="120"/>
            <column name="case_amount" title="مبلغ القضية" width="120" format="currency"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="datetime"/>
        </columns>
        
        <summary>
            <field name="total_follows" title="إجمالي المتابعات" type="count"/>
            <field name="completed_follows" title="المتابعات المكتملة" type="count" 
                   condition="status = 'completed'"/>
            <field name="pending_follows" title="المتابعات المعلقة" type="count" 
                   condition="status = 'pending'"/>
            <field name="total_case_amount" title="إجمالي مبالغ القضايا" type="sum" column="case_amount" format="currency"/>
        </summary>
        
        <charts>
            <chart name="follows_by_status" title="المتابعات حسب الحالة" type="pie" 
                   group-by="status" value="count"/>
            <chart name="follows_by_service" title="المتابعات حسب نوع الخدمة" type="bar" 
                   group-by="service_name" value="count"/>
            <chart name="follows_by_day" title="المتابعات حسب اليوم" type="line" 
                   group-by="DAY(date_field)" value="count"/>
        </charts>
    </report-view>
</form-module>

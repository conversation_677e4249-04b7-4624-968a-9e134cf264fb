/* ملف JavaScript بسيط للنظام المحاسبي */
console.log('تم تحميل النظام المحاسبي');

// وظائف أساسية
function showAlert(message) {
    alert(message);
}

function confirmDelete() {
    return confirm('هل أنت متأكد من الحذف؟');
}

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    console.log('النظام جاهز');
    
    // إضافة معالجات للأزرار
    var deleteButtons = document.querySelectorAll('.btn-danger');
    for (var i = 0; i < deleteButtons.length; i++) {
        deleteButtons[i].addEventListener('click', function(e) {
            if (!confirmDelete()) {
                e.preventDefault();
            }
        });
    }
});

// وظائف النظام القضائي
window.LegalSystem = {
    updateStatus: function(id, status) {
        console.log('تحديث الحالة:', id, status);
    }
};

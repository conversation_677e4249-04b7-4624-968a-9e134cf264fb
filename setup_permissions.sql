-- إعداد صلاحيات النظام القضائي
-- تاريخ الإنشاء: 2025-08-21

-- إنشاء جدول الصلاحيات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    module VARCHAR(50),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول ربط الأدوار بالصلاحيات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER,
    permission_id INTEGER REFERENCES permissions(id),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إضافة صلاحيات النظام القضائي
INSERT INTO permissions (name, description, module) VALUES
('legal_view', 'عرض النظام القضائي', 'legal'),
('legal_cases_view', 'عرض القضايا', 'legal'),
('legal_cases_add', 'إضافة قضايا جديدة', 'legal'),
('legal_cases_edit', 'تعديل القضايا', 'legal'),
('legal_cases_delete', 'حذف القضايا', 'legal'),
('legal_follows_view', 'عرض المتابعات', 'legal'),
('legal_follows_add', 'إضافة متابعات', 'legal'),
('legal_follows_edit', 'تعديل المتابعات', 'legal'),
('legal_follows_delete', 'حذف المتابعات', 'legal'),
('legal_reports_view', 'عرض التقارير القانونية', 'legal'),
('legal_settings_view', 'عرض إعدادات النظام القانوني', 'legal'),
('legal_settings_edit', 'تعديل إعدادات النظام القانوني', 'legal'),
('legal_admin', 'إدارة النظام القضائي بالكامل', 'legal')
ON CONFLICT (name) DO NOTHING;

-- إنشاء دور المدير القانوني إذا لم يكن موجوداً
INSERT INTO roles (name, description) VALUES
('legal_admin', 'مدير النظام القانوني'),
('legal_user', 'مستخدم النظام القانوني'),
('legal_viewer', 'مشاهد النظام القانوني')
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المدير القانوني
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'legal_admin' LIMIT 1),
    p.id
FROM permissions p
WHERE p.module = 'legal'
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المستخدم العادي
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'legal_user' LIMIT 1),
    p.id
FROM permissions p
WHERE p.name IN ('legal_view', 'legal_cases_view', 'legal_cases_add', 'legal_cases_edit', 
                 'legal_follows_view', 'legal_follows_add', 'legal_follows_edit', 'legal_reports_view')
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المشاهد
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'legal_viewer' LIMIT 1),
    p.id
FROM permissions p
WHERE p.name IN ('legal_view', 'legal_cases_view', 'legal_follows_view', 'legal_reports_view')
ON CONFLICT DO NOTHING;

-- إعطاء صلاحيات النظام القانوني للمدير العام (admin)
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'admin' LIMIT 1),
    p.id
FROM permissions p
WHERE p.module = 'legal'
ON CONFLICT DO NOTHING;

-- التحقق من الصلاحيات المضافة
SELECT 'تم إعداد الصلاحيات بنجاح' as الحالة,
       COUNT(*) as عدد_الصلاحيات
FROM permissions 
WHERE module = 'legal';

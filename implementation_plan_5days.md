# خطة التنفيذ السريعة - 5 أيام
## دمج النظام القضائي مع نظام mohhash المحاسبي

---

## 📅 **اليوم الأول: إعداد قاعدة البيانات والهجرة**

### الصباح (9:00 - 12:00):
#### 1. نسخ احتياطية وإعداد البيئة
```bash
# إنشاء نسخ احتياطية
pg_dump -U postgres -h localhost mohaminew > backup_legal_system_$(date +%Y%m%d).sql
pg_dump -U postgres -h localhost mohhash > backup_accounting_system_$(date +%Y%m%d).sql

# إنشاء مجلد العمل
mkdir D:\mohhash\Integration
cd D:\mohhash\Integration
```

#### 2. تنفيذ سكريپت إنشاء الجداول
```bash
# تنفيذ ملف إنشاء الجداول
psql -U postgres -h localhost -d mohhash -f migration_plan_complete.sql

# التحقق من إنشاء الجداول
psql -U postgres -h localhost -d mohhash -c "\dt"
```

### بعد الظهر (1:00 - 5:00):
#### 3. نقل البيانات من mohaminew إلى mohhash
```bash
# تنفيذ سكريپت نقل البيانات
psql -U postgres -h localhost -d mohhash -f data_migration_script.sql

# التحقق من نجاح النقل
psql -U postgres -h localhost -d mohhash -c "
SELECT 'issues' as table_name, COUNT(*) as count FROM issues
UNION ALL
SELECT 'follows', COUNT(*) FROM follows
UNION ALL
SELECT 'services', COUNT(*) FROM services;"
```

#### 4. إعداد الربط المحاسبي
```bash
# تنفيذ سكريپت التكامل المحاسبي
psql -U postgres -h localhost -d mohhash -f accounting_integration.sql
```

---

## 📅 **اليوم الثاني: تطوير نماذج القضايا الأساسية**

### الصباح (9:00 - 12:00):
#### 1. إنشاء نماذج إدارة القضايا
```bash
# نسخ ملفات النماذج إلى مجلد النظام
cp legal_system_forms.xml D:\mohhash\App\bin\webapp\app\fms\

# إنشاء ملفات النماذج الفردية
mkdir D:\mohhash\App\bin\webapp\app\fms\legal-issues
mkdir D:\mohhash\App\bin\webapp\app\fms\case-follows
mkdir D:\mohhash\App\bin\webapp\app\fms\case-distribution
```

#### 2. تطوير نموذج إدارة القضايا
```xml
<!-- D:\mohhash\App\bin\webapp\app\fms\legal-issues.xml -->
<!-- نسخ محتوى نموذج القضايا من legal_system_forms.xml -->
```

### بعد الظهر (1:00 - 5:00):
#### 3. تطوير نموذج أنواع القضايا
```xml
<!-- إنشاء نموذج بسيط لإدارة أنواع القضايا -->
<form-module name="issue-types" title="أنواع القضايا">
    <list-view name="list" title="قائمة أنواع القضايا">
        <data-source>
            SELECT id, name, description, is_active, created_date
            FROM issue_types
            ORDER BY name
        </data-source>
        <columns>
            <column name="name" title="اسم النوع" width="200"/>
            <column name="description" title="الوصف" width="300"/>
            <column name="is_active" title="نشط" width="100" format="boolean"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        <actions>
            <action name="add" title="إضافة نوع جديد" icon="plus"/>
            <action name="edit" title="تعديل" icon="edit"/>
            <action name="delete" title="حذف" icon="delete"/>
        </actions>
    </list-view>
</form-module>
```

#### 4. تطوير نموذج الخدمات
```xml
<!-- إنشاء نموذج إدارة الخدمات -->
<form-module name="services" title="إدارة الخدمات">
    <!-- محتوى مشابه لأنواع القضايا -->
</form-module>
```

---

## 📅 **اليوم الثالث: تطوير نماذج المتابعة والتوزيع**

### الصباح (9:00 - 12:00):
#### 1. تطوير نموذج متابعة القضايا
```bash
# إنشاء ملف نموذج المتابعات
cp case-follows section من legal_system_forms.xml إلى 
D:\mohhash\App\bin\webapp\app\fms\case-follows.xml
```

#### 2. تطوير نموذج توزيع القضايا
```bash
# إنشاء ملف نموذج التوزيع
cp case-distribution section من legal_system_forms.xml إلى
D:\mohhash\App\bin\webapp\app\fms\case-distribution.xml
```

### بعد الظهر (1:00 - 5:00):
#### 3. تطوير نماذج البيانات الأساسية
```xml
<!-- نموذج المحافظات -->
<form-module name="governorates" title="إدارة المحافظات">
    <list-view name="list">
        <data-source>
            SELECT id, name, code, is_active FROM governorates ORDER BY name
        </data-source>
    </list-view>
</form-module>

<!-- نموذج المحاكم -->
<form-module name="courts" title="إدارة المحاكم">
    <list-view name="list">
        <data-source>
            SELECT c.id, c.name, g.name as governorate_name, c.is_active
            FROM courts c
            LEFT JOIN governorates g ON c.governorate_id = g.id
            ORDER BY c.name
        </data-source>
    </list-view>
</form-module>
```

#### 4. اختبار النماذج الأساسية
```bash
# اختبار الوصول للنماذج
curl http://localhost/app/fms/?fm=legal-issues&cmd=list
curl http://localhost/app/fms/?fm=case-follows&cmd=list
```

---

## 📅 **اليوم الرابع: التقارير والتكامل المحاسبي**

### الصباح (9:00 - 12:00):
#### 1. تطوير تقارير القضايا
```xml
<!-- تقرير ملخص القضايا -->
<form-module name="legal-reports" title="التقارير القانونية">
    <report-view name="cases-summary" title="ملخص القضايا">
        <data-source>
            SELECT 
                case_number, title, client_name, status, amount,
                total_revenue, total_expenses, net_profit
            FROM cases_financial_summary
            ORDER BY net_profit DESC
        </data-source>
    </report-view>
</form-module>
```

#### 2. تطوير التقارير المالية للقضايا
```xml
<!-- تقرير الربحية -->
<report-view name="profitability" title="تقرير الربحية">
    <parameters>
        <parameter name="start_date" title="من تاريخ" type="date"/>
        <parameter name="end_date" title="إلى تاريخ" type="date"/>
    </parameters>
    <data-source>
        SELECT * FROM get_lawyer_profitability_report(@start_date, @end_date)
    </data-source>
</report-view>
```

### بعد الظهر (1:00 - 5:00):
#### 3. تطوير واجهات الربط المحاسبي
```xml
<!-- نموذج إنشاء قيود محاسبية للقضايا -->
<form-module name="case-accounting" title="المحاسبة القضائية">
    <form-view name="create-fee-entry" title="تسجيل استلام أتعاب">
        <fields>
            <field name="case_id" title="القضية" type="lookup" required="true"/>
            <field name="amount" title="المبلغ" type="decimal" required="true"/>
            <field name="description" title="الوصف" type="text" required="true"/>
            <field name="payment_method" title="طريقة الدفع" type="select"/>
        </fields>
        <save-action>
            SELECT create_fee_journal_entry(@case_id, @amount, @description, @payment_method)
        </save-action>
    </form-view>
</form-module>
```

#### 4. اختبار التكامل المحاسبي
```sql
-- اختبار إنشاء قيد محاسبي
SELECT create_fee_journal_entry(1, 1000.00, 'استلام أتعاب قضية رقم 2025/001', 'نقدي');

-- التحقق من القيد
SELECT * FROM journal_entries WHERE description LIKE '%قضية%';
```

---

## 📅 **اليوم الخامس: التكامل النهائي والاختبار**

### الصباح (9:00 - 12:00):
#### 1. إنشاء القائمة الرئيسية
```bash
# نسخ ملف القائمة الرئيسية
cp legal_menu_integration.html D:\mohhash\App\bin\webapp\app\legal_menu.html
```

#### 2. تحديث القائمة الرئيسية للنظام
```html
<!-- تحديث D:\mohhash\App\bin\webapp\Default.aspx -->
<!-- إضافة رابط النظام القانوني -->
<div class="main-menu-item">
    <a href="app/legal_menu.html" class="legal-system-link">
        <i class="fa fa-gavel"></i>
        النظام القانوني
    </a>
</div>
```

#### 3. إعداد الصلاحيات والأمان
```sql
-- إضافة صلاحيات النظام القانوني
INSERT INTO permissions (name, description) VALUES
('legal_view', 'عرض النظام القانوني'),
('legal_add', 'إضافة قضايا ومتابعات'),
('legal_edit', 'تعديل القضايا والمتابعات'),
('legal_delete', 'حذف القضايا والمتابعات'),
('legal_reports', 'عرض التقارير القانونية'),
('legal_accounting', 'ربط القضايا بالمحاسبة');

-- ربط الصلاحيات بالأدوار
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p 
WHERE r.name = 'admin' AND p.name LIKE 'legal_%';
```

### بعد الظهر (1:00 - 5:00):
#### 4. الاختبار الشامل
```bash
# اختبار جميع الوظائف
echo "اختبار إضافة قضية جديدة..."
curl -X POST http://localhost/app/fms/?fm=legal-issues&cmd=add \
  -d "case_number=TEST001&title=قضية اختبار&client_name=عميل تجريبي"

echo "اختبار إضافة متابعة..."
curl -X POST http://localhost/app/fms/?fm=case-follows&cmd=add \
  -d "case_id=1&service_id=1&report=تقرير اختبار"

echo "اختبار التقارير..."
curl http://localhost/app/fms/?fm=legal-reports&cmd=cases-summary
```

#### 5. التوثيق والتدريب
```markdown
# إنشاء دليل المستخدم
## كيفية استخدام النظام القضائي المتكامل

### 1. الوصول للنظام:
- افتح المتصفح على http://localhost
- اضغط على "النظام القانوني" من القائمة الرئيسية

### 2. إضافة قضية جديدة:
- اضغط "إدارة القضايا" → "إضافة قضية جديدة"
- املأ البيانات المطلوبة
- احفظ القضية

### 3. متابعة القضايا:
- اضغط "متابعة القضايا" → "إضافة متابعة جديدة"
- اختر القضية ونوع الخدمة
- اكتب تقرير المتابعة

### 4. التقارير المالية:
- اضغط "التقارير القانونية" → "التقارير المالية"
- اختر الفترة الزمنية
- اعرض أو صدّر التقرير
```

---

## ✅ **قائمة التحقق النهائية**

### قاعدة البيانات:
- [ ] تم إنشاء جميع الجداول المطلوبة
- [ ] تم نقل البيانات من mohaminew بنجاح
- [ ] تم إنشاء الربط المحاسبي
- [ ] تم اختبار الدوال والمحفزات

### النماذج والواجهات:
- [ ] نموذج إدارة القضايا يعمل
- [ ] نموذج متابعة القضايا يعمل
- [ ] نموذج توزيع القضايا يعمل
- [ ] نماذج البيانات الأساسية تعمل

### التقارير:
- [ ] تقرير ملخص القضايا
- [ ] التقارير المالية للقضايا
- [ ] تقرير أداء المحامين
- [ ] تقارير المتابعات

### التكامل:
- [ ] الربط مع النظام المحاسبي يعمل
- [ ] إنشاء القيود المحاسبية تلقائياً
- [ ] القائمة الرئيسية محدثة
- [ ] الصلاحيات مُعدة بشكل صحيح

### الاختبار:
- [ ] اختبار إضافة قضية جديدة
- [ ] اختبار إضافة متابعة
- [ ] اختبار التقارير
- [ ] اختبار التكامل المحاسبي

---

## 🎯 **النتيجة المتوقعة**

بعد 5 أيام من التطوير، ستحصل على:

1. **نظام قضائي متكامل** مدمج بالكامل مع نظام mohhash المحاسبي
2. **قاعدة بيانات موحدة** تحتوي على جميع البيانات
3. **واجهات ASP.NET** متسقة مع النظام الموجود
4. **ربط محاسبي تلقائي** لجميع العمليات المالية
5. **تقارير شاملة** للقضايا والأداء المالي
6. **نظام صلاحيات متكامل** مع النظام الموجود

**الوقت الإجمالي**: 5 أيام × 8 ساعات = 40 ساعة عمل فقط! 🚀

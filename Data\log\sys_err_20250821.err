21/8/2025 00:18:17 ;  ; 94 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 00:18:17 ;  ; 94 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



21/8/2025 00:21:43 ;  ; 109 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 00:21:43 ;  ; 109 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



21/8/2025 00:22:51 ;  ; 89 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 00:22:51 ;  ; 89 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



21/8/2025 00:52:33 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:52:33 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:52:33 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:52:33 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:52:33 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:52:33 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:02 ;  ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:02 ;  ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:02 ;  ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:02 ;  ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:02 ;  ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:02 ;  ; 16 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:15 ;  ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:15 ;  ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:15 ;  ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:15 ;  ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:15 ;  ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:53:15 ;  ; 18 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)



21/8/2025 00:54:32 ;  ; 22 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 00:54:32 ;  ; 22 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



21/8/2025 00:56:57 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:56:57 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:56:57 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:56:57 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:56:57 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:56:57 ;  ; 22 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)



21/8/2025 00:57:53 ;  ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:53 ;  ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:53 ;  ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:53 ;  ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:53 ;  ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:53 ;  ; 21 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:58 ;  ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:58 ;  ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:58 ;  ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:58 ;  ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:58 ;  ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


21/8/2025 00:57:58 ;  ; 10 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)



21/8/2025 00:58:52 ;  ; 18 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 00:58:52 ;  ; 18 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


21/8/2025 00:58:52 ;  ; 18 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)


21/8/2025 00:59:09 ;  ; 14 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 00:59:09 ;  ; 14 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



21/8/2025 00:59:13 ;  ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 00:59:13 ;  ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


21/8/2025 00:59:13 ;  ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)


21/8/2025 00:59:42 ;  ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 00:59:42 ;  ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



21/8/2025 01:01:29 ;  ; 9 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 01:01:29 ;  ; 9 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


21/8/2025 01:01:29 ;  ; 9 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)


21/8/2025 01:01:58 ;  ; 11 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 01:01:58 ;  ; 11 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


21/8/2025 01:01:58 ;  ; 11 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)


21/8/2025 01:02:00 ;  ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 01:02:00 ;  ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



21/8/2025 01:02:31 ;  ; 26 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 01:02:31 ;  ; 26 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


21/8/2025 01:02:31 ;  ; 26 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)


21/8/2025 01:02:57 ;  ; 16 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 01:02:57 ;  ; 16 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


21/8/2025 01:02:57 ;  ; 16 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)



21/8/2025 01:03:32 ;  ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 01:03:32 ;  ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


21/8/2025 01:03:32 ;  ; 29 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)



21/8/2025 01:04:13 ;  ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


21/8/2025 01:04:13 ;  ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


21/8/2025 01:04:13 ;  ; 20 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)




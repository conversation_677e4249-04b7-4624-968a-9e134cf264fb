<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج المحافظات - متوافق مع نظام RemoX -->
<form-module name="governorates" title="المحافظات" icon="map"
             permission="legal_settings_view" add-permission="legal_settings_edit"
             edit-permission="legal_settings_edit" delete-permission="legal_settings_edit">

    <!-- قائمة المحافظات -->
    <list-view name="list" title="قائمة المحافظات" permission="legal_settings_view">
        <data-source>
            SELECT 
                id, 
                name, 
                code,
                region,
                is_active, 
                created_date,
                (SELECT COUNT(*) FROM courts WHERE governorate_id = governorates.id) as courts_count,
                (SELECT COUNT(*) FROM issues i JOIN courts c ON i.court_id = c.id WHERE c.governorate_id = governorates.id) as cases_count
            FROM governorates
            ORDER BY name
        </data-source>

        <columns>
            <column name="name" title="اسم المحافظة" width="200" sortable="true"/>
            <column name="code" title="الرمز" width="80" sortable="true"/>
            <column name="region" title="المنطقة" width="120" sortable="true"/>
            <column name="courts_count" title="عدد المحاكم" width="80" align="center"/>
            <column name="cases_count" title="عدد القضايا" width="80" align="center"/>
            <column name="is_active" title="نشط" width="60" type="boolean"/>
            <column name="created_date" title="تاريخ الإنشاء" width="100" type="date"/>
        </columns>

        <filters>
            <filter name="name" title="اسم المحافظة" type="text" field="name"/>
            <filter name="region" title="المنطقة" type="select" field="region">
                <options>
                    <option value="">الكل</option>
                    <option value="الوسط">الوسط</option>
                    <option value="الشمال">الشمال</option>
                    <option value="الجنوب">الجنوب</option>
                </options>
            </filter>
            <filter name="is_active" title="الحالة" type="select" field="is_active">
                <options>
                    <option value="">الكل</option>
                    <option value="true">نشط</option>
                    <option value="false">غير نشط</option>
                </options>
            </filter>
        </filters>

        <actions>
            <action name="add" title="إضافة محافظة جديدة" icon="plus" color="primary"
                   permission="legal_settings_edit"/>
            <action name="edit" title="تعديل" icon="edit" color="warning"
                   permission="legal_settings_edit"/>
            <action name="delete" title="حذف" icon="delete" color="danger"
                   permission="legal_settings_edit" confirm="true"/>
            <action name="view" title="عرض التفاصيل" icon="eye" color="info"/>
            <action name="courts" title="محاكم المحافظة" icon="university" color="success"
                   url="/app/fms/?fm=courts&amp;cmd=list&amp;governorate_id={id}"/>
        </actions>

        <summary>
            <field name="total_governorates" title="إجمالي المحافظات" type="count"/>
            <field name="active_governorates" title="المحافظات النشطة" type="count" filter="is_active = true"/>
            <field name="total_courts" title="إجمالي المحاكم" type="sum" field="courts_count"/>
        </summary>
    </list-view>

    <!-- نموذج إضافة محافظة جديدة -->
    <form-view name="add" title="إضافة محافظة جديدة" permission="legal_settings_edit">
        <fields>
            <field name="name" title="اسم المحافظة" type="text" required="true" maxlength="100"
                   placeholder="مثال: بغداد"
                   help="اسم المحافظة"/>
            <field name="code" title="رمز المحافظة" type="text" required="true" maxlength="10"
                   placeholder="مثال: BGD"
                   help="رمز مختصر للمحافظة"/>
            <field name="region" title="المنطقة" type="select" required="true">
                <options>
                    <option value="الوسط">الوسط</option>
                    <option value="الشمال">الشمال</option>
                    <option value="الجنوب">الجنوب</option>
                </options>
            </field>
            <field name="notes" title="ملاحظات" type="textarea" rows="3"/>
            <field name="is_active" title="نشط" type="checkbox" default="true"/>
        </fields>

        <save-action>
            INSERT INTO governorates (
                name, code, region, notes, is_active, created_date
            ) VALUES (
                @name, @code, @region, @notes, @is_active, CURRENT_TIMESTAMP
            )
        </save-action>

        <validation>
            <rule field="name" type="required" message="اسم المحافظة مطلوب"/>
            <rule field="name" type="unique" table="governorates" message="اسم المحافظة موجود مسبقاً"/>
            <rule field="code" type="required" message="رمز المحافظة مطلوب"/>
            <rule field="code" type="unique" table="governorates" message="رمز المحافظة موجود مسبقاً"/>
            <rule field="region" type="required" message="المنطقة مطلوبة"/>
        </validation>
    </form-view>

    <!-- نموذج تعديل محافظة -->
    <form-view name="edit" title="تعديل بيانات المحافظة" permission="legal_settings_edit">
        <load-action>
            SELECT * FROM governorates WHERE id = @id
        </load-action>

        <fields>
            <field name="name" title="اسم المحافظة" type="text" required="true" maxlength="100"/>
            <field name="code" title="رمز المحافظة" type="text" required="true" maxlength="10"/>
            <field name="region" title="المنطقة" type="select" required="true">
                <options>
                    <option value="الوسط">الوسط</option>
                    <option value="الشمال">الشمال</option>
                    <option value="الجنوب">الجنوب</option>
                </options>
            </field>
            <field name="notes" title="ملاحظات" type="textarea" rows="3"/>
            <field name="is_active" title="نشط" type="checkbox"/>
        </fields>

        <save-action>
            UPDATE governorates SET 
                name = @name, 
                code = @code, 
                region = @region, 
                notes = @notes, 
                is_active = @is_active,
                updated_date = CURRENT_TIMESTAMP
            WHERE id = @id
        </save-action>

        <validation>
            <rule field="name" type="required" message="اسم المحافظة مطلوب"/>
            <rule field="code" type="required" message="رمز المحافظة مطلوب"/>
            <rule field="region" type="required" message="المنطقة مطلوبة"/>
        </validation>
    </form-view>

    <!-- نموذج عرض تفاصيل المحافظة -->
    <form-view name="view" title="تفاصيل المحافظة" permission="legal_settings_view" readonly="true">
        <load-action>
            SELECT 
                g.*,
                (SELECT COUNT(*) FROM courts WHERE governorate_id = g.id) as total_courts,
                (SELECT COUNT(*) FROM courts WHERE governorate_id = g.id AND is_active = true) as active_courts,
                (SELECT COUNT(*) FROM issues i JOIN courts c ON i.court_id = c.id WHERE c.governorate_id = g.id) as total_cases,
                (SELECT COUNT(*) FROM issues i JOIN courts c ON i.court_id = c.id WHERE c.governorate_id = g.id AND i.status = 'active') as active_cases
            FROM governorates g
            WHERE g.id = @id
        </load-action>

        <fields>
            <field name="name" title="اسم المحافظة" type="text" readonly="true"/>
            <field name="code" title="رمز المحافظة" type="text" readonly="true"/>
            <field name="region" title="المنطقة" type="text" readonly="true"/>
            <field name="total_courts" title="إجمالي المحاكم" type="number" readonly="true"/>
            <field name="active_courts" title="المحاكم النشطة" type="number" readonly="true"/>
            <field name="total_cases" title="إجمالي القضايا" type="number" readonly="true"/>
            <field name="active_cases" title="القضايا النشطة" type="number" readonly="true"/>
            <field name="notes" title="ملاحظات" type="textarea" readonly="true"/>
            <field name="is_active" title="نشط" type="checkbox" readonly="true"/>
            <field name="created_date" title="تاريخ الإنشاء" type="datetime" readonly="true"/>
        </fields>
    </form-view>

</form-module>

<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج إدارة الخدمات -->
<form-module name="services" title="إدارة الخدمات" icon="cogs">
    
    <list-view name="list" title="قائمة الخدمات">
        <data-source>
            SELECT 
                id, 
                name, 
                description, 
                default_percentage,
                is_active, 
                created_date,
                (SELECT COUNT(*) FROM follows WHERE service_id = services.id) as follows_count
            FROM services
            ORDER BY name
        </data-source>
        
        <columns>
            <column name="name" title="اسم الخدمة" width="200" sortable="true"/>
            <column name="description" title="الوصف" width="250"/>
            <column name="default_percentage" title="النسبة الافتراضية" width="120" format="percentage"/>
            <column name="follows_count" title="عدد المتابعات" width="100" format="number"/>
            <column name="is_active" title="نشط" width="100" format="boolean"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <filters>
            <filter name="is_active" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="true">نشط</option>
                    <option value="false">غير نشط</option>
                </options>
            </filter>
        </filters>
        
        <actions>
            <action name="add" title="إضافة خدمة جديدة" icon="plus" color="primary"/>
            <action name="edit" title="تعديل" icon="edit" color="warning"/>
            <action name="delete" title="حذف" icon="delete" color="danger" confirm="true"/>
            <action name="toggle_status" title="تغيير الحالة" icon="toggle-on" color="info"/>
        </actions>
    </list-view>
    
    <form-view name="add" title="إضافة خدمة جديدة">
        <fields>
            <field name="name" title="اسم الخدمة" type="text" required="true" 
                   placeholder="مثال: استشارة قانونية"/>
            <field name="description" title="وصف الخدمة" type="textarea" rows="4" 
                   placeholder="وصف تفصيلي للخدمة"/>
            <field name="default_percentage" title="النسبة الافتراضية %" type="decimal" 
                   placeholder="0.00" min="0" max="100"/>
            <field name="is_active" title="نشط" type="checkbox" default="true"/>
        </fields>
        
        <save-action>
            INSERT INTO services (name, description, default_percentage, is_active)
            VALUES (@name, @description, @default_percentage, @is_active)
        </save-action>
        
        <update-action>
            UPDATE services SET
                name = @name,
                description = @description,
                default_percentage = @default_percentage,
                is_active = @is_active
            WHERE id = @id
        </update-action>
    </form-view>
    
    <form-view name="edit" title="تعديل الخدمة">
        <fields>
            <field name="name" title="اسم الخدمة" type="text" required="true"/>
            <field name="description" title="وصف الخدمة" type="textarea" rows="4"/>
            <field name="default_percentage" title="النسبة الافتراضية %" type="decimal" 
                   min="0" max="100"/>
            <field name="is_active" title="نشط" type="checkbox"/>
        </fields>
        
        <load-action>
            SELECT name, description, default_percentage, is_active 
            FROM services WHERE id = @id
        </load-action>
        
        <update-action>
            UPDATE services SET
                name = @name,
                description = @description,
                default_percentage = @default_percentage,
                is_active = @is_active
            WHERE id = @id
        </update-action>
    </form-view>
</form-module>

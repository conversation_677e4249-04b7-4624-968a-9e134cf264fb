2025/08/22 00:13:22.910 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 00:13:22.911 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 00:13:22.911 ; Log ; 1 ;  ; 0000: ; 22/8/2025 00:13:22 @Http Req#0
2025/08/22 00:13:22.911 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 00:13:22.911 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 00:13:22.912 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 00:13:22.918 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 00:13:22.921 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 00:13:22.987 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 00:13:22.987 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 00:13:23.071 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 00:13:23.076 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 00:13:23.167 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 00:13:23.173 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 00:13:23.205 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 00:13:23.206 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 00:13:23.207 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 00:13:23.210 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 00:13:23.250 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 00:13:23.250 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 00:13:23.252 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 00:13:23.253 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 00:13:23.260 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 00:13:23.378 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 00:13:23.378 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 00:13:23.378 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 00:13:23.378 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 00:13:23.379 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 00:13:23.379 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 00:13:23.379 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 00:13:23.514 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 00:13:24.369 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.370 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.395 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.421 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.442 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.442 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:24.448 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:13:28.380 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 00:13:28.381 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 00:13:28.384 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 00:13:31.915 ; Log ; 9 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s

2025/08/22 00:23:56.522 ; Log ; 29 ; ::1 ; 9900:admin ; System Restart:  @Http Req#9 @Req#8 0s
2025/08/22 00:23:57.053 ; Log ; 13 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/22 00:23:57.054 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/22 00:23:57.054 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/22 00:23:57.108 ; Log ; 13 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/22 00:32:13.875 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 00:32:13.876 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 00:32:13.877 ; Log ; 1 ;  ; 0000: ; 22/8/2025 00:32:13 @Http Req#0
2025/08/22 00:32:13.877 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 00:32:13.877 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 00:32:13.877 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 00:32:13.884 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 00:32:13.888 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 00:32:13.969 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 00:32:13.969 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 00:32:14.048 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 00:32:14.055 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 00:32:14.155 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 00:32:14.159 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 00:32:14.188 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 00:32:14.188 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 00:32:14.189 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 00:32:14.191 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 00:32:14.234 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 00:32:14.234 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 00:32:14.238 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 00:32:14.240 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 00:32:14.246 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 00:32:14.368 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 00:32:14.368 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 00:32:14.368 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 00:32:14.369 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 00:32:14.369 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 00:32:14.370 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 00:32:14.370 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 00:32:14.479 ; Log ; 132 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 00:32:15.254 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.255 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.279 ; Log ; 134 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.306 ; Log ; 134 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.326 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.326 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:15.331 ; Log ; 134 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 00:32:19.372 ; Log ; 140 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 00:32:19.374 ; Log ; 140 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 00:32:19.371 ; Log ; 135 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2

2025/08/22 00:38:04.291 ; Error ; 133 ; ::1 ; 9900: ; Failed login attempt @Http Req#3 @Req#2 0s
2025/08/22 00:38:04.292 ; Log ; 133 ; ::1 ; 9900: ; Failed user login attempt: admin @Http Req#3 @Req#2 0s
2025/08/22 00:38:04.292 ; Log ; 133 ; ::1 ; 9900: ; اسم المستخدم أو كلمة السر غير صحيح ( E35 )  @Http Req#3 @Req#2 0s ; #at:UserError
2025/08/22 00:38:13.181 ; Log ; 146 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#4 @Req#3 0s

2025/08/22 01:01:50.948 ; Log ; 132 ; ::1 ; 9900:admin ; System Restart:  @Http Req#8 @Req#7 0s
2025/08/22 01:01:50.962 ; Log ; 136 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#8
2025/08/22 01:01:50.962 ; Log ; 135 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#8
2025/08/22 01:01:50.962 ; Log ; 140 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#8
2025/08/22 01:01:50.970 ; Log ; 136 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#8

2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; 22/8/2025 01:01:51 @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 01:01:51.084 ; Log ; 170 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 01:01:51.087 ; Log ; 170 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 01:01:51.091 ; Log ; 170 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 01:01:51.151 ; Log ; 170 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 01:01:51.151 ; Log ; 170 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 01:01:51.228 ; Log ; 170 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 01:01:51.233 ; Log ; 170 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 01:01:51.306 ; Log ; 170 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 01:01:51.312 ; Log ; 170 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 01:01:51.341 ; Log ; 170 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 01:01:51.342 ; Log ; 170 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 01:01:51.342 ; Log ; 170 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 01:01:51.345 ; Log ; 170 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 01:01:51.401 ; Log ; 170 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 01:01:51.401 ; Log ; 170 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 01:01:51.403 ; Log ; 170 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 01:01:51.404 ; Log ; 170 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 01:01:51.414 ; Log ; 170 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 01:01:51.536 ; Log ; 170 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 01:01:51.537 ; Log ; 170 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 01:01:51.537 ; Log ; 170 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 01:01:51.537 ; Log ; 170 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 01:01:51.537 ; Log ; 170 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 01:01:51.538 ; Log ; 170 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 01:01:51.539 ; Log ; 170 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 01:01:51.652 ; Log ; 143 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 01:01:56.541 ; Log ; 173 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#1
2025/08/22 01:01:56.541 ; Log ; 172 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#1
2025/08/22 01:01:56.544 ; Log ; 173 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#1

2025/08/22 01:49:23.586 ; Log ; 125 ;  ; 0000: ; Initiating app shutdown, waiting for worker threads... @Http Req#1
2025/08/22 01:49:23.586 ; Log ; 172 ;  ; 0000: ; Ending threaded queue: DelayedSqlQueue @Http Req#1
2025/08/22 01:49:23.586 ; Log ; 173 ;  ; 0000: ; Ending threaded queue: ScheduledTasksQueue @Http Req#1
2025/08/22 01:49:23.643 ; Log ; 125 ;  ; 0000: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#1

2025/08/22 03:24:14.805 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 03:24:14.808 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 03:24:14.808 ; Log ; 1 ;  ; 0000: ; 22/8/2025 03:24:14 @Http Req#0
2025/08/22 03:24:14.809 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 03:24:14.809 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 03:24:14.809 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 03:24:14.820 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 03:24:14.823 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 03:24:14.920 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 03:24:14.923 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 03:24:15.007 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 03:24:15.015 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 03:24:15.134 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 03:24:15.153 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 03:24:15.249 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 03:24:15.255 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 03:24:15.270 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 03:24:15.285 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 03:24:15.389 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 03:24:15.389 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 03:24:15.390 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 03:24:15.391 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 03:24:15.397 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 03:24:15.615 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 03:24:15.615 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 03:24:15.616 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 03:24:15.616 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 03:24:15.616 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 03:24:15.617 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 03:24:15.618 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 03:24:15.740 ; Log ; 22 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 03:24:17.135 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.136 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.166 ; Log ; 14 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.187 ; Log ; 14 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.214 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.216 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:17.221 ; Log ; 14 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 03:24:20.619 ; Log ; 32 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 03:24:20.620 ; Log ; 33 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 03:24:20.626 ; Log ; 33 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 03:24:25.978 ; Log ; 12 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s

2025/08/22 03:26:23.716 ; Log ; 26 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#5
2025/08/22 03:26:23.716 ; Log ; 33 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#5
2025/08/22 03:26:23.716 ; Log ; 32 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#5
2025/08/22 03:26:23.719 ; Log ; 26 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#5

2025/08/22 04:19:19.325 ; Log ; 49 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; 22/8/2025 04:19:19 @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 04:19:19.326 ; Log ; 49 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 04:19:19.331 ; Log ; 49 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 04:19:19.336 ; Log ; 49 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 04:19:19.406 ; Log ; 49 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 04:19:19.406 ; Log ; 49 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 04:19:19.471 ; Log ; 49 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 04:19:19.475 ; Log ; 49 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 04:19:19.560 ; Log ; 49 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 04:19:19.565 ; Log ; 49 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 04:19:19.591 ; Log ; 49 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 04:19:19.591 ; Log ; 49 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 04:19:19.592 ; Log ; 49 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 04:19:19.597 ; Log ; 49 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 04:19:19.650 ; Log ; 49 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 04:19:19.651 ; Log ; 49 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 04:19:19.651 ; Log ; 49 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 04:19:19.652 ; Log ; 49 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 04:19:19.652 ; Log ; 49 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 04:19:19.766 ; Log ; 49 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 04:19:19.767 ; Log ; 49 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 04:19:19.767 ; Log ; 49 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 04:19:19.896 ; Log ; 51 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 04:19:20.722 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.722 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.742 ; Log ; 66 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.764 ; Log ; 66 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.775 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.775 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:20.779 ; Log ; 66 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:19:24.768 ; Log ; 69 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 04:19:24.769 ; Log ; 70 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 04:19:24.772 ; Log ; 70 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 04:19:28.510 ; Log ; 14 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s

2025/08/22 04:23:05.822 ; Log ; 53 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Req#12 0s ; #at:UserError
2025/08/22 04:23:22.451 ; Log ; 19 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Req#13 0s ; #at:UserError

2025/08/22 04:23:28.756 ; Log ; 64 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=daily @Req#14 0s ; #at:UserError

2025/08/22 04:25:56.563 ; Log ; 17 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=daily @Req#18 0s ; #at:UserError
2025/08/22 04:26:04.447 ; Log ; 36 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=list @Req#19 0s ; #at:UserError
2025/08/22 04:26:11.857 ; Log ; 15 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=cases-summary @Req#20 0s ; #at:UserError

2025/08/22 04:26:50.801 ; Log ; 15 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=cases-summary @Req#21 0s ; #at:UserError
2025/08/22 04:26:53.073 ; Log ; 14 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=cases-summary @Req#22 0s ; #at:UserError

2025/08/22 04:27:26.916 ; Log ; 15 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Req#23 0s ; #at:UserError

2025/08/22 04:29:56.283 ; Log ; 57 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/22 04:29:56.283 ; Log ; 69 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/22 04:29:56.283 ; Log ; 70 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/22 04:29:56.336 ; Log ; 57 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/22 04:30:32.877 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 04:30:32.885 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 04:30:32.885 ; Log ; 1 ;  ; 0000: ; 22/8/2025 04:30:32 @Http Req#0
2025/08/22 04:30:32.885 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 04:30:32.885 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 04:30:32.886 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 04:30:32.896 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 04:30:32.901 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 04:30:32.988 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 04:30:32.990 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 04:30:33.099 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 04:30:33.106 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 04:30:33.248 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 04:30:33.274 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 04:30:33.386 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 04:30:33.395 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 04:30:33.418 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 04:30:33.438 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 04:30:33.550 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 04:30:33.550 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 04:30:33.551 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 04:30:33.552 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 04:30:33.559 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 04:30:33.793 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 04:30:33.794 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 04:30:33.794 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 04:30:33.794 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 04:30:33.796 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 04:30:33.796 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 04:30:33.797 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 04:30:33.973 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 04:30:35.531 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:30:35.533 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:30:35.564 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 04:30:35.592 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 04:30:35.615 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:30:35.615 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:30:35.620 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:30:38.798 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 04:30:38.798 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 04:30:38.805 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 04:30:41.328 ; Log ; 21 ; ::1 ; 9900: ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Http Req#3 @Req#2 0s ; #at:UserError
2025/08/22 04:30:44.876 ; Log ; 22 ; ::1 ; 9900: ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Http Req#4 @Req#3 0s ; #at:UserError
2025/08/22 04:30:49.729 ; Log ; 23 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#5 @Req#4 0s
2025/08/22 04:30:50.749 ; Log ; 20 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=daily @Http Req#8 @Req#7 0s ; #at:UserError

2025/08/22 04:34:20.064 ; Log ; 43 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#8
2025/08/22 04:34:20.064 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#8
2025/08/22 04:34:20.064 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#8
2025/08/22 04:34:20.069 ; Log ; 43 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#8

2025/08/22 04:34:51.231 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/22 04:34:51.243 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/22 04:34:51.269 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/22 04:34:51.269 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 04:34:51.270 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 04:34:51.270 ; Log ; 1 ;  ; 0000: ; 22/8/2025 04:34:51 @Http Req#0
2025/08/22 04:34:51.271 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 04:34:51.271 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 04:34:51.271 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 04:34:51.275 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 04:34:51.279 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 04:34:51.338 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 04:34:51.338 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 04:34:51.386 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/22 04:34:51.414 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 04:34:51.417 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 04:34:51.422 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/22 04:34:51.436 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/22 04:34:51.436 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/22 04:34:51.437 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/22 04:34:51.439 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/22 04:34:51.443 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/22 04:34:51.497 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 04:34:51.501 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 04:34:51.533 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 04:34:51.534 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 04:34:51.534 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 04:34:51.536 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 04:34:51.586 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/22 04:34:51.588 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 04:34:51.588 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 04:34:51.589 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 04:34:51.590 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 04:34:51.597 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 04:34:51.601 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/22 04:34:51.708 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 04:34:51.708 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 04:34:51.709 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 04:34:51.709 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 04:34:51.709 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 04:34:51.710 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 04:34:51.710 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 04:34:51.769 ; Trace ; 27 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/22 04:34:51.776 ; Trace ; 27 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/22 04:34:51.776 ; Trace ; 27 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/22 04:34:51.800 ; Trace ; 27 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/22 04:34:51.841 ; Trace ; 27 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/22 04:34:51.842 ; Trace ; 27 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/22 04:34:51.846 ; Log ; 27 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 04:34:51.856 ; Trace ; 27 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/22 04:34:52.036 ; Trace ; 27 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/22 04:34:52.196 ; Trace ; 27 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/22 04:34:52.627 ; Trace ; 27 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/22 04:34:52.638 ; Info ; 46 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.653 ; Log ; 46 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.654 ; Log ; 46 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.658 ; Trace ; 46 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.674 ; Trace ; 46 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.674 ; Trace ; 46 ; ::1 ; 9900: ; Accounts loaded:69 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.674 ; Trace ; 46 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.677 ; Trace ; 46 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.679 ; Trace ; 46 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.681 ; Log ; 46 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.709 ; Log ; 46 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.709 ; Trace ; 46 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.717 ; Trace ; 46 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.719 ; Log ; 46 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.719 ; Log ; 46 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.723 ; Log ; 46 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:52.723 ; Info ; 46 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:34:56.710 ; Log ; 15 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 04:34:56.711 ; Log ; 14 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 04:34:56.714 ; Log ; 14 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 04:35:00.010 ; Trace ; 25 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/22 04:35:00.037 ; Log ; 25 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/22 04:35:00.037 ; Trace ; 25 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/22 04:35:00.051 ; Info ; 37 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/22 04:35:00.111 ; Info ; 24 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/22 04:35:56.720 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#5

2025/08/22 04:36:03.145 ; Info ; 37 ; ::1 ; 9900:admin ;  ;  ; /app/?about&sind=y ; ::1 @Http Req#6 @Req#5 0s
2025/08/22 04:36:56.722 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#6

2025/08/22 04:37:56.724 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#6

2025/08/22 04:38:41.589 ; Log ; 44 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=daily @Http Req#7 @Req#6 0s ; #at:UserError
2025/08/22 04:38:46.336 ; Log ; 30 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=daily @Http Req#8 @Req#7 0s ; #at:UserError
2025/08/22 04:38:55.051 ; Log ; 17 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Http Req#9 @Req#8 0s ; #at:UserError
2025/08/22 04:38:56.726 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#9

2025/08/22 04:39:30.206 ; Log ; 47 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Req#9 0s ; #at:UserError
2025/08/22 04:39:39.974 ; Log ; 29 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Req#10 0s ; #at:UserError
2025/08/22 04:39:50.054 ; Log ; 26 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-issues ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-issues&cmd=list @Req#11 0s ; #at:UserError
2025/08/22 04:39:53.433 ; Info ; 31 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#12 0s
2025/08/22 04:39:56.727 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:39:56.885 ; Info ; 35 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#13 0s
2025/08/22 04:40:13.664 ; Info ; 56 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#14 0s
2025/08/22 04:40:38.306 ; Log ; 58 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( case-follows ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=case-follows&cmd=add @Req#15 0s ; #at:UserError
2025/08/22 04:40:44.216 ; Log ; 46 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( case-follows ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=case-follows&cmd=calendar @Req#16 0s ; #at:UserError
2025/08/22 04:40:56.727 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:41:56.728 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:42:09.303 ; Info ; 37 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=check ; ::1 @Req#17 0s
2025/08/22 04:42:09.658 ; Trace ; 37 ; ::1 ; 9900:admin ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Req#17 0s
2025/08/22 04:42:10.306 ; Fatal ; 37 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#17 1s ; #at:RunSql_GetReader
2025/08/22 04:42:10.307 ; Log ; 37 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#17 1s ; #at:RunSql_GetReader
2025/08/22 04:42:10.309 ; Fatal ; 37 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#17 1s ; #at:GetDbTableSchema
2025/08/22 04:42:10.309 ; Log ; 37 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#17 1s ; #at:GetDbTableSchema
2025/08/22 04:42:10.529 ; Fatal ; 37 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond) @Req#17 1s ; #at:GetRecordsCount
2025/08/22 04:42:10.529 ; Log ; 37 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#17 1s ; #at:GetRecordsCount
2025/08/22 04:42:15.971 ; Info ; 43 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update ; ::1 @Req#18 0s
2025/08/22 04:42:16.497 ; Fatal ; 43 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#18 0s ; #at:RunSql_GetReader
2025/08/22 04:42:16.497 ; Log ; 43 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#18 0s ; #at:RunSql_GetReader
2025/08/22 04:42:16.497 ; Fatal ; 43 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#18 0s ; #at:GetDbTableSchema
2025/08/22 04:42:16.497 ; Log ; 43 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#18 0s ; #at:GetDbTableSchema
2025/08/22 04:42:16.902 ; Log ; 43 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#18 0s
2025/08/22 04:42:22.133 ; Info ; 54 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Req#19 0s
2025/08/22 04:42:56.729 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:43:56.735 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:44:56.736 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:45:51.771 ; Info ; 30 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Req#20 0s
2025/08/22 04:45:56.736 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:46:00.106 ; Info ; 13 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update&sind=y ; ::1 @Req#21 0s
2025/08/22 04:46:00.712 ; Fatal ; 13 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#21 0s ; #at:RunSql_GetReader
2025/08/22 04:46:00.712 ; Log ; 13 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#21 0s ; #at:RunSql_GetReader
2025/08/22 04:46:00.712 ; Fatal ; 13 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#21 0s ; #at:GetDbTableSchema
2025/08/22 04:46:00.712 ; Log ; 13 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#21 0s ; #at:GetDbTableSchema
2025/08/22 04:46:01.046 ; Log ; 13 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#21 0s
2025/08/22 04:46:10.151 ; Info ; 18 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=list-tables&sind=y ; ::1 @Req#22 0s
2025/08/22 04:46:56.737 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:47:56.740 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:48:04.823 ; Info ; 55 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=update ; ::1 @Req#23 0s
2025/08/22 04:48:05.450 ; Fatal ; 55 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql) @Req#23 0s ; #at:RunSql_GetReader
2025/08/22 04:48:05.450 ; Log ; 55 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#23 0s ; #at:RunSql_GetReader
2025/08/22 04:48:05.451 ; Fatal ; 55 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name) @Req#23 0s ; #at:GetDbTableSchema
2025/08/22 04:48:05.451 ; Log ; 55 ; ::1 ; 9900:admin ; Invalid object name 'hr_shift_sched'. @Req#23 0s ; #at:GetDbTableSchema
2025/08/22 04:48:05.758 ; Log ; 55 ; ::1 ; 9900:admin ; 
Database is updated sucessfully. @Req#23 0s
2025/08/22 04:48:13.338 ; Info ; 37 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#24 0s
2025/08/22 04:48:29.184 ; Info ; 57 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#25 0s
2025/08/22 04:48:35.742 ; Log ; 54 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( case-follows ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=case-follows&cmd=list @Req#26 0s ; #at:UserError
2025/08/22 04:48:53.403 ; Log ; 57 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( case-follows ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=case-follows&cmd=add @Req#27 0s ; #at:UserError
2025/08/22 04:48:56.742 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:49:49.393 ; Dev ; 55 ; ::1 ; 9900:admin ; >> Form is cached: user @Req#28 0s
2025/08/22 04:49:49.394 ; Info ; 55 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Req#28 0s
2025/08/22 04:49:52.356 ; Dev ; 27 ; ::1 ; 9900:admin ; >> Form is cached: auth-role @Req#29 0s
2025/08/22 04:49:52.356 ; Info ; 27 ; ::1 ; 9900:admin ; auth-role ;  ; /app/fms/?fm=auth-role ; ::1 @Req#29 0s
2025/08/22 04:49:56.745 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:49:57.955 ; Dev ; 18 ; ::1 ; 9900:admin ; Form loaded from cached: auth-role @Req#30 0s
2025/08/22 04:49:57.955 ; Info ; 18 ; ::1 ; 9900:admin ; auth-role ;  ; /app/fms/?fm=auth-role&cmd=add ; ::1 @Req#30 0s
2025/08/22 04:50:37.149 ; Dev ; 33 ; ::1 ; 9900:admin ; Form loaded from cached: auth-role @Req#31 0s
2025/08/22 04:50:37.149 ; Info ; 33 ; ::1 ; 9900:admin ; auth-role ;  ; /app/fms/?fm=auth-role ; ::1 @Req#31 0s
2025/08/22 04:50:40.565 ; Dev ; 66 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#32 0s
2025/08/22 04:50:40.565 ; Info ; 66 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Req#32 0s
2025/08/22 04:50:42.857 ; Dev ; 54 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#33 0s
2025/08/22 04:50:42.857 ; Info ; 54 ; ::1 ; 9900:admin ; user ;  ; /app/fms/?fm=user ; ::1 @Req#33 0s
2025/08/22 04:50:44.590 ; Dev ; 43 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#34 0s
2025/08/22 04:50:44.590 ; Info ; 43 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin&sind=y ; ::1 @Req#34 0s
2025/08/22 04:50:48.506 ; Dev ; 35 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#35 0s
2025/08/22 04:50:48.506 ; Info ; 35 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=edit&id=admin ; ::1 @Req#35 0s
2025/08/22 04:50:56.747 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:51:20.437 ; Dev ; 43 ; ::1 ; 9900:admin ; Form loaded from cached: user @Req#36 0s
2025/08/22 04:51:20.437 ; Info ; 43 ; ::1 ; 9900:admin ; user ; admin ; /app/fms/?fm=user&cmd=view&id=admin&sind=y ; ::1 @Req#36 0s
2025/08/22 04:51:21.576 ; Fatal ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.576 ; Log ; 47 ; ::1 ; 9900:admin ; parent_fld @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.577 ; Fatal ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.577 ; Log ; 47 ; ::1 ; 9900:admin ; parent_fld @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.577 ; Fatal ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.577 ; Log ; 47 ; ::1 ; 9900:admin ; parent_fld @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.577 ; Fatal ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.577 ; Log ; 47 ; ::1 ; 9900:admin ; parent_fld @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.577 ; Fatal ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.577 ; Log ; 47 ; ::1 ; 9900:admin ; parent_fld @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.578 ; Fatal ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name) @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.578 ; Log ; 47 ; ::1 ; 9900:admin ; parent_fld @Req#37 0s ; #at:ReadStr:parent_fld
2025/08/22 04:51:21.598 ; Dev ; 47 ; ::1 ; 9900:admin ; LoadTableFieldsDef: user_attr  items:25 @Req#37 0s
2025/08/22 04:51:21.599 ; Info ; 47 ; ::1 ; 9900:admin ; attrs ; admin ; /app/fms/?fm=attrs&obj_type=user&id=admin&pfm=user&pid=admin ; ::1 @Req#37 0s
2025/08/22 04:51:56.749 ; Dev ; 14 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/22 04:52:31.588 ; Info ; 52 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#38 0s
2025/08/22 04:52:34.792 ; Info ; 35 ; ::1 ; 9900:admin ;  ;  ; /app/?restart ; ::1 @Req#39 0s
2025/08/22 04:52:34.794 ; Log ; 35 ; ::1 ; 9900:admin ; System Restart:  @Req#39 0s
2025/08/22 04:52:34.811 ; Trace ; 64 ;  ; 9900: ; Session End: User logged out
2025/08/22 04:52:34.811 ; Log ; 64 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads...
2025/08/22 04:52:34.811 ; Log ; 15 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue
2025/08/22 04:52:34.811 ; Log ; 14 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue
2025/08/22 04:52:34.819 ; Log ; 64 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ********

2025/08/22 04:52:34.890 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/22 04:52:34.903 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/22 04:52:34.972 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/22 04:52:34.973 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/22 04:52:34.974 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/22 04:52:34.974 ; Log ; 1 ;  ; 0000: ; 22/8/2025 04:52:34 @Http Req#0
2025/08/22 04:52:34.974 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/22 04:52:34.974 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 646  @Http Req#0
2025/08/22 04:52:34.975 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/22 04:52:34.983 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/22 04:52:34.987 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/22 04:52:35.053 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/22 04:52:35.054 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/22 04:52:35.106 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/22 04:52:35.134 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/22 04:52:35.138 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/22 04:52:35.145 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/22 04:52:35.169 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/22 04:52:35.169 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/22 04:52:35.169 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/22 04:52:35.174 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/22 04:52:35.181 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/22 04:52:35.251 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/22 04:52:35.254 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/22 04:52:35.297 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/22 04:52:35.297 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/22 04:52:35.298 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/22 04:52:35.301 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/22 04:52:35.321 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/22 04:52:35.324 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/22 04:52:35.324 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/22 04:52:35.328 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/22 04:52:35.329 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/22 04:52:35.336 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/22 04:52:35.341 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 1 @Http Req#0
2025/08/22 04:52:35.446 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/22 04:52:35.446 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/22 04:52:35.446 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/22 04:52:35.447 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/22 04:52:35.447 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/22 04:52:35.447 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/22 04:52:35.448 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/22 04:52:35.465 ; Trace ; 55 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/22 04:52:35.471 ; Trace ; 55 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/22 04:52:35.471 ; Trace ; 55 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/22 04:52:35.502 ; Trace ; 55 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/22 04:52:35.549 ; Trace ; 55 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/22 04:52:35.550 ; Trace ; 55 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/22 04:52:35.553 ; Log ; 55 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/22 04:52:35.560 ; Trace ; 55 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/22 04:52:35.712 ; Trace ; 55 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/22 04:52:35.858 ; Trace ; 55 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/22 04:52:36.301 ; Trace ; 55 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/22 04:52:36.309 ; Info ; 65 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.330 ; Log ; 65 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.331 ; Log ; 65 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.335 ; Trace ; 65 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.351 ; Trace ; 65 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.351 ; Trace ; 65 ; ::1 ; 9900: ; Accounts loaded:69 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.351 ; Trace ; 65 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.353 ; Trace ; 65 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.355 ; Trace ; 65 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.356 ; Log ; 65 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.374 ; Log ; 65 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.374 ; Trace ; 65 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.384 ; Trace ; 65 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.387 ; Log ; 65 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.387 ; Log ; 65 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.391 ; Log ; 65 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:36.391 ; Info ; 65 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/22 04:52:40.448 ; Log ; 69 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/22 04:52:40.449 ; Log ; 70 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/22 04:52:40.453 ; Log ; 70 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/22 04:52:42.758 ; Trace ; 52 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/22 04:52:42.779 ; Log ; 52 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/22 04:52:42.779 ; Trace ; 52 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/22 04:52:42.794 ; Info ; 26 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/22 04:52:42.855 ; Info ; 35 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/22 04:52:58.906 ; Info ; 33 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#6 @Req#5 0s
2025/08/22 04:53:00.581 ; Log ; 68 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( legal-reports ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=legal-reports&cmd=cases-summary @Http Req#7 @Req#6 0s ; #at:UserError
2025/08/22 04:53:40.460 ; Dev ; 70 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#7

2025/08/22 04:54:40.463 ; Dev ; 70 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#7

2025/08/22 04:55:40.464 ; Dev ; 70 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#7

2025/08/22 04:56:28.011 ; Info ; 50 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Http Req#8 @Req#7 0s
2025/08/22 04:56:40.466 ; Dev ; 70 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#8


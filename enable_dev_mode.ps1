# تفعيل وضع المطور للنظام المحاسبي RemoX
# تاريخ الإنشاء: 2025-08-21

Write-Host "🔧 تفعيل وضع المطور للنظام المحاسبي RemoX..." -ForegroundColor Green

# مسار ملف web.config
$webConfigPath = "D:\RemoX\App\bin\webapp\Web.config"
$backupPath = "D:\RemoX\App\bin\webapp\Web.config.backup"

# إنشاء نسخة احتياطية من web.config
if (Test-Path $webConfigPath) {
    Write-Host "📋 إنشاء نسخة احتياطية من web.config..." -ForegroundColor Yellow
    Copy-Item $webConfigPath $backupPath -Force
    Write-Host "✅ تم إنشاء النسخة الاحتياطية: $backupPath" -ForegroundColor Green
} else {
    Write-Host "❌ لم يتم العثور على ملف web.config في: $webConfigPath" -ForegroundColor Red
}

# تفعيل وضع التطوير في IIS
Write-Host "🔧 تفعيل وضع التطوير في IIS..." -ForegroundColor Yellow

try {
    # تفعيل detailed errors
    & "$env:windir\system32\inetsrv\appcmd.exe" set config "Default Web Site" -section:system.webServer/httpErrors -errorMode:Detailed
    Write-Host "✅ تم تفعيل الأخطاء التفصيلية" -ForegroundColor Green
    
    # تفعيل directory browsing
    & "$env:windir\system32\inetsrv\appcmd.exe" set config "Default Web Site" -section:system.webServer/directoryBrowse -enabled:true
    Write-Host "✅ تم تفعيل تصفح المجلدات" -ForegroundColor Green
    
    # تفعيل debugging
    & "$env:windir\system32\inetsrv\appcmd.exe" set config "Default Web Site" -section:system.web/compilation -debug:true
    Write-Host "✅ تم تفعيل وضع التطوير" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️ تحذير: قد تحتاج لتشغيل PowerShell كمدير لتطبيق بعض الإعدادات" -ForegroundColor Yellow
}

# إعادة تشغيل IIS
Write-Host "🔄 إعادة تشغيل IIS..." -ForegroundColor Yellow
try {
    & iisreset
    Write-Host "✅ تم إعادة تشغيل IIS بنجاح" -ForegroundColor Green
} catch {
    Write-Host "⚠️ تحذير: فشل في إعادة تشغيل IIS. قد تحتاج لتشغيل الأمر كمدير" -ForegroundColor Yellow
}

# تفعيل مراقبة الملفات للتطوير
Write-Host "📁 تفعيل مراقبة الملفات للتطوير..." -ForegroundColor Yellow

# إنشاء ملف مراقبة للتغييرات
$watcherScript = @"
# مراقب الملفات للتطوير
`$watcher = New-Object System.IO.FileSystemWatcher
`$watcher.Path = "D:\RemoX\App\bin\webapp\app\fms\"
`$watcher.Filter = "*.xml"
`$watcher.IncludeSubdirectories = `$true
`$watcher.EnableRaisingEvents = `$true

`$action = {
    `$path = `$Event.SourceEventArgs.FullPath
    `$changeType = `$Event.SourceEventArgs.ChangeType
    `$timeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$timeStamp] تم تعديل الملف: `$path - نوع التغيير: `$changeType" -ForegroundColor Cyan
}

Register-ObjectEvent -InputObject `$watcher -EventName "Changed" -Action `$action
Register-ObjectEvent -InputObject `$watcher -EventName "Created" -Action `$action
Register-ObjectEvent -InputObject `$watcher -EventName "Deleted" -Action `$action

Write-Host "🔍 مراقب الملفات نشط. اضغط Ctrl+C للإيقاف..." -ForegroundColor Green
try {
    while (`$true) {
        Start-Sleep 1
    }
} finally {
    `$watcher.Dispose()
}
"@

$watcherScript | Out-File -FilePath "D:\RemoX\file_watcher.ps1" -Encoding UTF8
Write-Host "✅ تم إنشاء مراقب الملفات: D:\RemoX\file_watcher.ps1" -ForegroundColor Green

# عرض معلومات الوصول
Write-Host "`n🌐 معلومات الوصول للنظام:" -ForegroundColor Cyan
Write-Host "   الرابط الرئيسي: http://localhost/" -ForegroundColor White
Write-Host "   النظام القضائي: http://localhost/app/legal_system_menu.html" -ForegroundColor White
Write-Host "   قائمة القضايا: http://localhost/app/fms/?fm=legal-issues&cmd=list" -ForegroundColor White

Write-Host "`n📋 أوامر مفيدة للتطوير:" -ForegroundColor Cyan
Write-Host "   مراقبة الملفات: PowerShell -File D:\RemoX\file_watcher.ps1" -ForegroundColor White
Write-Host "   إعادة تشغيل IIS: iisreset" -ForegroundColor White
Write-Host "   عرض سجلات IIS: Get-EventLog -LogName Application -Source 'ASP.NET*' -Newest 10" -ForegroundColor White

Write-Host "`n🎯 وضع المطور نشط الآن!" -ForegroundColor Green
Write-Host "   - الأخطاء التفصيلية مفعلة" -ForegroundColor White
Write-Host "   - تصفح المجلدات مفعل" -ForegroundColor White
Write-Host "   - وضع التطوير مفعل" -ForegroundColor White
Write-Host "   - مراقب الملفات جاهز" -ForegroundColor White
"@

$enableDevScript | Out-File -FilePath "enable_dev_mode.ps1" -Encoding UTF8
Write-Host "✅ تم إنشاء سكريپت تفعيل وضع المطور" -ForegroundColor Green

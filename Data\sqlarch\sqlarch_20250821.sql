/* #0001 21/8/2025 00:23:22 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0002 21/8/2025 00:23:22 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0003 21/8/2025 00:23:22 (1) */
UPDATE hs_config SET cfg_value='kriqvaeVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0004 21/8/2025 00:23:22 (1) */
UPDATE hs_config SET cfg_value='kKGv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0005 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0006 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0007 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0008 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0009 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0010 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0011 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0012 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0013 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0014 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0015 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0016 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0017 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0018 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0019 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0020 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0021 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0022 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0023 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0024 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0025 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0026 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0027 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0028 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0029 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0030 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0031 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0032 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0033 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0034 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0035 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0036 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0037 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0038 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0039 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0040 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0041 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0042 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0043 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0044 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0045 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0046 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0047 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0048 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0049 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0050 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0051 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0052 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0053 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0054 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0055 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0056 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0057 21/8/2025 00:23:22 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0058 21/8/2025 00:23:22 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0059 21/8/2025 00:23:37 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JR5RKFYTH1',NULL,'SYS','admin','Successfull login','********','002337','W',0,0,NULL,'sys',NULL)
GO
/* #0060 21/8/2025 00:23:37 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:54239 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********002337','9900')
GO
/* #0061 21/8/2025 00:23:37 (1) */
UPDATE hs_logindata SET last_activity_dt='********002337' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0062 21/8/2025 00:25:18 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JR68569FY',NULL,'SYS','admin','Successfull login','********','002518','W',0,0,NULL,'sys',NULL)
GO
/* #0063 21/8/2025 00:25:18 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:54518 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********002518','9900')
GO
/* #0064 21/8/2025 00:25:18 (1) */
UPDATE hs_logindata SET last_activity_dt='********002518' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0001 21/8/2025 00:26:27 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0002 21/8/2025 00:26:27 (1) */
UPDATE hs_config SET cfg_value='l7ivvaeVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0003 21/8/2025 00:26:27 (1) */
UPDATE hs_config SET cfg_value='kaGv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0004 21/8/2025 00:26:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0005 21/8/2025 00:26:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0006 21/8/2025 00:26:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0007 21/8/2025 00:26:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0008 21/8/2025 00:26:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0009 21/8/2025 00:26:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0010 21/8/2025 00:26:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0011 21/8/2025 00:26:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0012 21/8/2025 00:26:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0013 21/8/2025 00:26:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0014 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0015 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0016 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0017 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0018 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0019 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0020 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0021 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0022 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0023 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0024 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0025 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0026 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0027 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0028 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0029 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0030 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0031 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0032 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0033 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0034 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0035 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0036 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0037 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0038 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0039 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0040 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0041 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0042 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0043 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0044 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0045 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0046 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0047 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0048 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0049 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0050 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0051 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0052 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0053 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0054 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0055 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0056 21/8/2025 00:26:28 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0057 21/8/2025 00:26:28 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0058 21/8/2025 00:26:55 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JR6OBLWMP1',NULL,'SYS','admin','Successfull login','********','002655','W',0,0,NULL,'sys',NULL)
GO
/* #0059 21/8/2025 00:26:55 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:54518 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','**************','9900')
GO
/* #0060 21/8/2025 00:26:55 (1) */
UPDATE hs_logindata SET last_activity_dt='**************' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0061 21/8/2025 00:27:33 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys',NULL,'x-shutdown','alert',' @: Last shutdown was not clean, check the system','**************','9900')
GO
/* #0062 21/8/2025 00:28:50 (1) */
INSERT INTO fi_accounts (fi_acc_no,ma_acc_no,acc_name,acc_parent,acc_hier_cat,acc_depth,acc_nat,acc_status,acc_report,acc_root,acc_crncy,linked_acc_no,acc_type,acc_flags,crtd_by,crtd_date,crtd_time,sys_client_id) VALUES ('8003','8003','انا هو ','128','2',4,-1,'1','1','1','01','1280',8,0,'admin','********','002850','9900')
GO
/* #0063 21/8/2025 00:28:50 (0) */
DELETE FROM hs_relations WHERE sys_client_id='9900' AND rel_type='EPWD' AND parent_id='8003'
GO
/* #0064 21/8/2025 00:28:50 (1) */
INSERT INTO hr_emp_data (emp_no,emp_name,emp_title,emp_status,emp_start_date,emp_term_date,emp_pay_type,emp_basic_sal,sal_crncy,week_hours,hour_rate,emp_shift,ot_policy,emp_notes,wk_off_days,emp_flags,emp_dob,emp_mar_status,emp_nat,emp_id_type,emp_id_no,emp_ssn,emp_tax_code,emp_emrgncy_contact,emp_edu_degree,emp_edu_text,emp_pos_no,emp_supv_no,emp_grade,emp_dept,emp_section,emp_ou,emp_grp,branch,proj_id,actvty_id,cc_no,emp_acc_no,emp_work_geo,emp_ess_mach,emp_work_locs,emp_leave_bal,emp_cto_bal,emp_loans,emp_advances,last_ts_run_dt,last_py_run_dt,approved,emp_term_reason,emp_term_note,sys_client_id) VALUES ('8003','انا هو ','مدير ','9','20250807',NULL,'1',555555,'01',40,3205.125,NULL,'P',NULL,'5;',279280,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'8003',NULL,NULL,NULL,12.12,0,0,0,NULL,NULL,'N',NULL,NULL,'9900')
GO
/* #0065 21/8/2025 00:28:50 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('hr-emp','admin','8003','add',NULL,'********002850','9900')
GO
/* #0066 21/8/2025 00:30:27 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JR7NDFR1A',NULL,'SYS','admin','Successfull login','********','003027','W',0,0,NULL,'sys',NULL)
GO
/* #0067 21/8/2025 00:30:27 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:55006 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********003027','9900')
GO
/* #0068 21/8/2025 00:30:27 (1) */
UPDATE hs_logindata SET last_activity_dt='********003027' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0001 21/8/2025 00:31:53 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0002 21/8/2025 00:31:53 (1) */
UPDATE hs_config SET cfg_value='k7+ovKeVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0003 21/8/2025 00:31:53 (1) */
UPDATE hs_config SET cfg_value='kqGv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0004 21/8/2025 00:31:53 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0005 21/8/2025 00:31:53 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0006 21/8/2025 00:31:53 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0007 21/8/2025 00:31:53 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0008 21/8/2025 00:31:53 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0009 21/8/2025 00:31:53 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0010 21/8/2025 00:31:53 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0011 21/8/2025 00:31:53 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0012 21/8/2025 00:31:53 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0013 21/8/2025 00:31:53 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0014 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0015 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0016 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0017 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0018 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0019 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0020 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0021 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0022 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0023 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0024 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0025 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0026 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0027 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0028 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0029 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0030 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0031 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0032 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0033 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0034 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0035 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0036 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0037 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0038 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0039 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0040 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0041 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0042 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0043 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0044 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0045 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0046 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0047 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0048 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0049 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0050 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0051 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0052 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0053 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0054 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0055 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0056 21/8/2025 00:31:54 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0057 21/8/2025 00:31:54 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0058 21/8/2025 00:32:01 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JR82UZ63T1',NULL,'SYS','admin','Successfull login','********','003201','W',0,0,NULL,'sys',NULL)
GO
/* #0059 21/8/2025 00:32:01 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:55005 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********003201','9900')
GO
/* #0060 21/8/2025 00:32:01 (1) */
UPDATE hs_logindata SET last_activity_dt='********003201' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0061 21/8/2025 00:32:08 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys',NULL,'x-shutdown','alert',' @: Last shutdown was not clean, check the system','********003208','9900')
GO
/* #0062 21/8/2025 00:32:08 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0001 21/8/2025 00:32:30 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0002 21/8/2025 00:32:31 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0003 21/8/2025 00:32:31 (1) */
UPDATE hs_config SET cfg_value='kLmrvKeVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0004 21/8/2025 00:32:31 (1) */
UPDATE hs_config SET cfg_value='k6Gv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0005 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0006 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0007 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0008 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0009 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0010 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0011 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0012 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0013 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0014 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0015 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0016 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0017 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0018 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0019 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0020 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0021 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0022 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0023 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0024 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0025 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0026 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0027 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0028 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0029 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0030 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0031 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0032 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0033 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0034 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0035 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0036 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0037 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0038 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0039 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0040 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0041 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0042 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0043 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0044 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0045 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0046 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0047 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0048 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0049 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0050 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0051 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0052 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0053 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0054 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0055 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0056 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0057 21/8/2025 00:32:31 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0058 21/8/2025 00:32:31 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0059 21/8/2025 00:32:36 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JR88MGOPG1',NULL,'SYS','admin','Successfull login','********','003236','W',0,0,NULL,'sys',NULL)
GO
/* #0060 21/8/2025 00:32:36 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:55005 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********003236','9900')
GO
/* #0061 21/8/2025 00:32:36 (1) */
UPDATE hs_logindata SET last_activity_dt='********003236' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0062 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:c-20900'
GO
/* #0063 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:c-20900')
GO
/* #0064 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:def_wk_off_days'
GO
/* #0065 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:def_wk_off_days')
GO
/* #0066 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value='30' WHERE cfg_key='9900:emp_annual_leaves'
GO
/* #0067 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES ('30','9900:emp_annual_leaves')
GO
/* #0068 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:salary_tax_pct'
GO
/* #0069 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:salary_tax_pct')
GO
/* #0070 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:ss_emp_pct'
GO
/* #0071 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:ss_emp_pct')
GO
/* #0072 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:ss_comp_pct'
GO
/* #0073 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:ss_comp_pct')
GO
/* #0074 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:c-51810'
GO
/* #0075 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:c-51810')
GO
/* #0076 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value='200' WHERE cfg_key='9900:hcm_emp_max_distance'
GO
/* #0077 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES ('200','9900:hcm_emp_max_distance')
GO
/* #0078 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:c-48626'
GO
/* #0079 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:c-48626')
GO
/* #0080 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:at_accrued_wages_acc'
GO
/* #0081 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:at_accrued_wages_acc')
GO
/* #0082 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:accrued_wages_acc'
GO
/* #0083 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:accrued_wages_acc')
GO
/* #0084 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:at_def_wages_exp_acc'
GO
/* #0085 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:at_def_wages_exp_acc')
GO
/* #0086 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:def_wages_exp_acc'
GO
/* #0087 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:def_wages_exp_acc')
GO
/* #0088 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:c-26815'
GO
/* #0089 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:c-26815')
GO
/* #0090 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:alt_period_start'
GO
/* #0091 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:alt_period_start')
GO
/* #0092 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='9900:alt_period_end'
GO
/* #0093 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES (NULL,'9900:alt_period_end')
GO
/* #0094 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value='16910352' WHERE cfg_key='9900:hcm_sys_opts'
GO
/* #0095 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES ('16910352','9900:hcm_sys_opts')
GO
/* #0096 21/8/2025 00:33:39 (0) */
UPDATE hs_config SET cfg_value='1' WHERE cfg_key='9900:hr-sys_svd'
GO
/* #0097 21/8/2025 00:33:39 (1) */
INSERT INTO hs_config (cfg_value,cfg_key) VALUES ('1','9900:hr-sys_svd')
GO
/* #0098 21/8/2025 00:33:39 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys-cfg','admin','hr-sys','change',NULL,'********003339','9900')
GO
/* #0099 21/8/2025 00:37:21 (0) */
DELETE FROM hs_objects WHERE sys_client_id='9900' AND (obj_type='MENU' AND owner_id='admin' AND obj_id='USERFAV')
GO
/* #0100 21/8/2025 00:37:21 (1) */
INSERT INTO hs_objects (obj_type,owner_id,obj_id,obj_flags,obj_media,obj_data,chgd_by,chgd_time,sys_client_id) VALUES ('MENU','admin','USERFAV',0,'X','<?xml version="1.0" encoding="utf-16"?>
<HsMenu xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <title>المفضلة</title>
  <css_class>icon_menu</css_class>
  <show_drop_down_sign>false</show_drop_down_sign>
  <add_to_sys_user_default_menu>true</add_to_sys_user_default_menu>
  <is_app_menu>false</is_app_menu>
  <items>
    <HsMenuItem>
      <type>Link</type>
      <text>الموظفين</text>
      <url>hr_menu.html</url>
      <open_in_dlg>false</open_in_dlg>
    </HsMenuItem>
  </items>
</HsMenu>','admin','********003721','9900')
GO
/* #0101 21/8/2025 00:43:57 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0001 21/8/2025 00:43:57 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0002 21/8/2025 00:43:57 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0003 21/8/2025 00:43:57 (1) */
UPDATE hs_config SET cfg_value='l7+qu6eVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0004 21/8/2025 00:43:57 (1) */
UPDATE hs_config SET cfg_value='lKGv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0005 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0006 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0007 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0008 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0009 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0010 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0011 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0012 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0013 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0014 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0015 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0016 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0017 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0018 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0019 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0020 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0021 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0022 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0023 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0024 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0025 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0026 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0027 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0028 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0029 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0030 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0031 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0032 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0033 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0034 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0035 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0036 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0037 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0038 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0039 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0040 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0041 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0042 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0043 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0044 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0045 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0046 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0047 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0048 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0049 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0050 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0051 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0052 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0053 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0054 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0055 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0056 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0057 21/8/2025 00:43:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0058 21/8/2025 00:43:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0059 21/8/2025 00:44:06 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRBEP08K01',NULL,'SYS','admin','Successfull login','********','004406','W',0,0,NULL,'sys',NULL)
GO
/* #0060 21/8/2025 00:44:06 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:56236 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********004406','9900')
GO
/* #0061 21/8/2025 00:44:06 (1) */
UPDATE hs_logindata SET last_activity_dt='********004406' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0062 21/8/2025 00:44:38 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRBK1EBDA',NULL,'SYS','admin','Successfull login','********','004438','W',0,0,NULL,'sys',NULL)
GO
/* #0063 21/8/2025 00:44:38 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:56299 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********004438','9900')
GO
/* #0064 21/8/2025 00:44:38 (1) */
UPDATE hs_logindata SET last_activity_dt='********004438' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0065 21/8/2025 00:47:04 (1) */
UPDATE hs_texts SET cfg_value=NULL WHERE cfg_key='sys_msg_text'
GO
/* #0066 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sys_msg_valid_from'
GO
/* #0067 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sys_msg_valid_to'
GO
/* #0068 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='remember_last_user'
GO
/* #0069 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='enable_remember_me'
GO
/* #0070 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='hash_user_pwd_during_login'
GO
/* #0071 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='enable_user_multi_logins'
GO
/* #0072 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='force_user_data_add_confirm'
GO
/* #0073 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='force_user_data_change_confirm'
GO
/* #0074 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='12288' WHERE cfg_key='app_options'
GO
/* #0075 21/8/2025 00:47:04 (1) */
UPDATE hs_texts SET cfg_value=NULL WHERE cfg_key='sys_hp_content'
GO
/* #0076 21/8/2025 00:47:04 (1) */
UPDATE hs_texts SET cfg_value=NULL WHERE cfg_key='sys_login_page_cont'
GO
/* #0077 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='enable_email_notif_chan'
GO
/* #0078 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='email_smtp_server'
GO
/* #0079 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='25' WHERE cfg_key='email_smtp_port'
GO
/* #0080 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='admin' WHERE cfg_key='email_server_user'
GO
/* #0081 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='email_default_from'
GO
/* #0082 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='email_default_subject'
GO
/* #0083 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='enable_sms_notif_chan'
GO
/* #0084 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_adapter'
GO
/* #0085 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_server'
GO
/* #0086 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='0' WHERE cfg_key='sms_gw_port'
GO
/* #0087 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_user'
GO
/* #0088 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_default_from'
GO
/* #0089 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='967' WHERE cfg_key='hs-sms.def_country_code'
GO
/* #0090 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_testers'
GO
/* #0091 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_params'
GO
/* #0092 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='admin_alerts_enable'
GO
/* #0093 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='admin_alerts_to_emails'
GO
/* #0094 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='admin_alerts_to_mobiles'
GO
/* #0095 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='ss-trns;ss-vend;sstore;hcm-ess;hcm-sys;es-procure;es-stock;fi-gl;cms;' WHERE cfg_key='app_active_menus'
GO
/* #0096 21/8/2025 00:47:04 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys-cfg','admin','hs-sys','change','app_active_menus :  => ss-trns;ss-vend;sstore;hcm-ess;hcm-sys;es-procure;es-stock;fi-gl;cms;
','********004704','9900')
GO
/* #0097 21/8/2025 00:47:04 (1) */
UPDATE hs_config SET cfg_value='17' WHERE cfg_key='hs-sys_svd'
GO
/* #0098 21/8/2025 00:47:04 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys-cfg','admin','hs-sys','change','app_active_menus :  => ss-trns;ss-vend;sstore;hcm-ess;hcm-sys;es-procure;es-stock;fi-gl;cms;
','********004704','9900')
GO
/* #0099 21/8/2025 00:47:09 (1) */
UPDATE hs_texts SET cfg_value=NULL WHERE cfg_key='sys_msg_text'
GO
/* #0100 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sys_msg_valid_from'
GO
/* #0101 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sys_msg_valid_to'
GO
/* #0102 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='remember_last_user'
GO
/* #0103 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='enable_remember_me'
GO
/* #0104 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='hash_user_pwd_during_login'
GO
/* #0105 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='enable_user_multi_logins'
GO
/* #0106 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='force_user_data_add_confirm'
GO
/* #0107 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='force_user_data_change_confirm'
GO
/* #0108 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='12288' WHERE cfg_key='app_options'
GO
/* #0109 21/8/2025 00:47:09 (1) */
UPDATE hs_texts SET cfg_value=NULL WHERE cfg_key='sys_hp_content'
GO
/* #0110 21/8/2025 00:47:09 (1) */
UPDATE hs_texts SET cfg_value=NULL WHERE cfg_key='sys_login_page_cont'
GO
/* #0111 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='enable_email_notif_chan'
GO
/* #0112 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='email_smtp_server'
GO
/* #0113 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='25' WHERE cfg_key='email_smtp_port'
GO
/* #0114 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='admin' WHERE cfg_key='email_server_user'
GO
/* #0115 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='email_default_from'
GO
/* #0116 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='email_default_subject'
GO
/* #0117 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='enable_sms_notif_chan'
GO
/* #0118 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_adapter'
GO
/* #0119 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_server'
GO
/* #0120 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='0' WHERE cfg_key='sms_gw_port'
GO
/* #0121 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_user'
GO
/* #0122 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_default_from'
GO
/* #0123 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='967' WHERE cfg_key='hs-sms.def_country_code'
GO
/* #0124 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_testers'
GO
/* #0125 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='sms_gw_params'
GO
/* #0126 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='admin_alerts_enable'
GO
/* #0127 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='admin_alerts_to_emails'
GO
/* #0128 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value=NULL WHERE cfg_key='admin_alerts_to_mobiles'
GO
/* #0129 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='ss-trns;ss-vend;sstore;hcm-ess;hcm-sys;es-procure;es-stock;fi-gl;cms;' WHERE cfg_key='app_active_menus'
GO
/* #0130 21/8/2025 00:47:09 (1) */
UPDATE hs_config SET cfg_value='18' WHERE cfg_key='hs-sys_svd'
GO
/* #0131 21/8/2025 00:47:09 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys-cfg','admin','hs-sys','change',NULL,'********004709','9900')
GO
/* #0132 21/8/2025 00:47:09 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys-cfg','admin','hs-sys','edit',NULL,'********004709','9900')
GO
/* #0133 21/8/2025 00:47:20 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','logout','http IP=::1:56299 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********004720','9900')
GO
/* #0134 21/8/2025 00:47:26 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRCBWPM6B',NULL,'SYS','admin','Successfull login','********','004726','W',0,0,NULL,'sys',NULL)
GO
/* #0135 21/8/2025 00:47:26 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:56299 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********004726','9900')
GO
/* #0136 21/8/2025 00:47:26 (1) */
UPDATE hs_logindata SET last_activity_dt='********004726' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0137 21/8/2025 00:48:01 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0001 21/8/2025 00:48:01 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0002 21/8/2025 00:48:01 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0003 21/8/2025 00:48:01 (1) */
UPDATE hs_config SET cfg_value='kbqhu6eVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0004 21/8/2025 00:48:01 (1) */
UPDATE hs_config SET cfg_value='laGv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0005 21/8/2025 00:48:01 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0006 21/8/2025 00:48:01 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0007 21/8/2025 00:48:01 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0008 21/8/2025 00:48:01 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0009 21/8/2025 00:48:01 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0010 21/8/2025 00:48:01 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0011 21/8/2025 00:48:01 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0012 21/8/2025 00:48:01 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0013 21/8/2025 00:48:01 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0014 21/8/2025 00:48:01 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0015 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0016 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0017 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0018 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0019 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0020 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0021 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0022 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0023 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0024 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0025 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0026 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0027 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0028 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0029 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0030 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0031 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0032 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0033 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0034 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0035 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0036 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0037 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0038 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0039 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0040 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0041 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0042 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0043 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0044 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0045 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0046 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0047 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0048 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0049 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0050 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0051 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0052 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0053 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0054 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0055 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0056 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0057 21/8/2025 00:48:02 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0058 21/8/2025 00:48:02 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0059 21/8/2025 00:48:09 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRCJ063OW1',NULL,'SYS','admin','Successfull login','********','004809','W',0,0,NULL,'sys',NULL)
GO
/* #0060 21/8/2025 00:48:09 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:56511 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********004809','9900')
GO
/* #0061 21/8/2025 00:48:09 (1) */
UPDATE hs_logindata SET last_activity_dt='********004809' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0001 21/8/2025 00:51:11 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0002 21/8/2025 00:51:11 (1) */
UPDATE hs_config SET cfg_value='kbuouqeVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0003 21/8/2025 00:51:11 (1) */
UPDATE hs_config SET cfg_value='lqGv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0004 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0005 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0006 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0007 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0008 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0009 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0010 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0011 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0012 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0013 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0014 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0015 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0016 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0017 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0018 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0019 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0020 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0021 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0022 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0023 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0024 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0025 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0026 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0027 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0028 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0029 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0030 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0031 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0032 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0033 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0034 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0035 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0036 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0037 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0038 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0039 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0040 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0041 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0042 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0043 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0044 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0045 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0046 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0047 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0048 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0049 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0050 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0051 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0052 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0053 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0054 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0055 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0056 21/8/2025 00:51:11 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0057 21/8/2025 00:51:11 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0058 21/8/2025 00:51:20 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRDEK1U3M',NULL,'SYS','admin','Successfull login','********','005120','W',0,0,NULL,'sys',NULL)
GO
/* #0059 21/8/2025 00:51:20 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:56835 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********005120','9900')
GO
/* #0060 21/8/2025 00:51:20 (1) */
UPDATE hs_logindata SET last_activity_dt='********005120' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0061 21/8/2025 00:52:17 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys',NULL,'x-shutdown','alert',' @: Last shutdown was not clean, check the system','********005217','9900')
GO
/* #0062 21/8/2025 00:52:25 (3) */
DELETE FROM hs_relations WHERE sys_client_id='9900' AND rel_type='USRL' AND parent_id='admin'
GO
/* #0063 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','all_admin','9900')
GO
/* #0064 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','app_adm','9900')
GO
/* #0065 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','app_dev','9900')
GO
/* #0066 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','usr_adm','9900')
GO
/* #0067 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-user','9900')
GO
/* #0068 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-admin','9900')
GO
/* #0069 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-so-mem','9900')
GO
/* #0070 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-so-vend','9900')
GO
/* #0071 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-so-trns','9900')
GO
/* #0072 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-so-admn','9900')
GO
/* #0073 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','RL000001','9900')
GO
/* #0074 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','RL000002','9900')
GO
/* #0075 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','RL000003','9900')
GO
/* #0076 21/8/2025 00:52:25 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','RL000004','9900')
GO
/* #0077 21/8/2025 00:52:25 (1) */
UPDATE hs_logindata SET user_name = 'متجر المتاجر - A STORE THE STORE',user_status = '1',valid_from = NULL,valid_to = NULL,user_group = NULL,user_subgroup = NULL,user_flags = 0,fi_acc_no = NULL,sys_client_id = '9900'  WHERE ( (sys_client_id = '9900') AND ((user_group IS NULL) ) ) AND ( user_id = 'admin' )
GO
/* #0078 21/8/2025 00:52:25 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('user','admin','admin','change','user_roles : all_admin;es-admin;es-user; => all_admin;app_adm;app_dev;usr_adm;es-user;es-admin;es-so-mem;es-so-vend;es-so-trns;es-so-admn;RL000001;RL000002;RL000003;RL000004;
','********005225','9900')
GO
/* #0079 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_hp'
GO
/* #0080 21/8/2025 00:53:02 (1) */
UPDATE hs_obj_attribs SET attr_value='dkufi' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_theme'
GO
/* #0081 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_ips'
GO
/* #0082 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_machs'
GO
/* #0083 21/8/2025 00:53:02 (1) */
UPDATE hs_obj_attribs SET attr_value='sstore;es-sales;es-stock;fi-gl;sys-menu;cms;es-procure;hcm-sys;hcm-ess;ss-vend;ss-trns;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_menus'
GO
/* #0084 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_cash_id'
GO
/* #0085 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_bank_id'
GO
/* #0086 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_branch_id'
GO
/* #0087 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_proj_id'
GO
/* #0088 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_sales_rep'
GO
/* #0089 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_sales_region'
GO
/* #0090 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_branch'
GO
/* #0091 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value='12311;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_teller'
GO
/* #0092 21/8/2025 00:53:02 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','admin','auth_usr_teller','12311;','9900')
GO
/* #0093 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_banks'
GO
/* #0094 21/8/2025 00:53:02 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','admin','auth_usr_banks','*;','9900')
GO
/* #0095 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_cc'
GO
/* #0096 21/8/2025 00:53:02 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','admin','auth_usr_cc','*;','9900')
GO
/* #0097 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_proj'
GO
/* #0098 21/8/2025 00:53:02 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','admin','auth_usr_proj','*;','9900')
GO
/* #0099 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_actv'
GO
/* #0100 21/8/2025 00:53:02 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','admin','auth_usr_actv','*;','9900')
GO
/* #0101 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_accs'
GO
/* #0102 21/8/2025 00:53:02 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','admin','auth_usr_accs','*;','9900')
GO
/* #0103 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='max_discount_pct'
GO
/* #0104 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_store_id'
GO
/* #0105 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value='14;12;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_sales'
GO
/* #0106 21/8/2025 00:53:02 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','admin','auth_usr_sales','14;12;','9900')
GO
/* #0107 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_store'
GO
/* #0108 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='usr_item_grps'
GO
/* #0109 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_item_coll'
GO
/* #0110 21/8/2025 00:53:02 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_prod_line'
GO
/* #0111 21/8/2025 00:53:02 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('user','admin','admin','change','user_menus : sstore;es-sales;es-stock;fi-gl;sys-menu; => sstore;es-sales;es-stock;fi-gl;sys-menu;cms;es-procure;hcm-sys;hcm-ess;ss-vend;ss-trns;
auth_usr_teller :  => 12311;
auth_usr_banks :  => *;
auth_usr_cc :  => *;
auth_usr_proj :  => *;
auth_usr_act','********005302','9900')
GO
/* #0112 21/8/2025 00:53:02 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('user','admin','admin','edit',NULL,'********005302','9900')
GO
/* #0113 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value='ana' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_hp'
GO
/* #0114 21/8/2025 00:53:15 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','admin','user_hp','ana','9900')
GO
/* #0115 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='dkufi' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_theme'
GO
/* #0116 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_ips'
GO
/* #0117 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_machs'
GO
/* #0118 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='sstore;es-sales;es-stock;fi-gl;sys-menu;cms;es-procure;hcm-sys;hcm-ess;ss-vend;ss-trns;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='user_menus'
GO
/* #0119 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_cash_id'
GO
/* #0120 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_bank_id'
GO
/* #0121 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_branch_id'
GO
/* #0122 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_proj_id'
GO
/* #0123 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_sales_rep'
GO
/* #0124 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_sales_region'
GO
/* #0125 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_branch'
GO
/* #0126 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='12311;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_teller'
GO
/* #0127 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_banks'
GO
/* #0128 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_cc'
GO
/* #0129 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_proj'
GO
/* #0130 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_actv'
GO
/* #0131 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='*;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_accs'
GO
/* #0132 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='max_discount_pct'
GO
/* #0133 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_store_id'
GO
/* #0134 21/8/2025 00:53:15 (1) */
UPDATE hs_obj_attribs SET attr_value='14;12;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_sales'
GO
/* #0135 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='auth_usr_store'
GO
/* #0136 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='usr_item_grps'
GO
/* #0137 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_item_coll'
GO
/* #0138 21/8/2025 00:53:15 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='admin' AND attr_key='def_prod_line'
GO
/* #0139 21/8/2025 00:53:15 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('user','admin','admin','change','user_hp :  => ana
','********005315','9900')
GO
/* #0140 21/8/2025 00:53:15 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('user','admin','admin','edit',NULL,'********005315','9900')
GO
/* #0141 21/8/2025 00:53:22 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','logout','http IP=::1:56904 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********005322','9900')
GO
/* #0142 21/8/2025 00:53:29 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRDZSOW9O',NULL,'SYS','admin','Successfull login','********','005329','W',0,0,NULL,'sys',NULL)
GO
/* #0143 21/8/2025 00:53:29 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:56834 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********005329','9900')
GO
/* #0144 21/8/2025 00:53:29 (1) */
UPDATE hs_logindata SET last_activity_dt='********005329' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0145 21/8/2025 00:53:43 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0001 21/8/2025 00:53:43 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0002 21/8/2025 00:53:43 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0003 21/8/2025 00:53:43 (1) */
UPDATE hs_config SET cfg_value='k76quqeVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0004 21/8/2025 00:53:43 (1) */
UPDATE hs_config SET cfg_value='l6Gv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0005 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0006 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0007 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0008 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0009 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0010 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0011 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0012 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0013 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0014 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0015 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0016 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0017 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0018 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0019 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0020 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0021 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0022 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0023 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0024 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0025 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0026 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0027 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0028 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0029 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0030 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0031 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0032 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0033 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0034 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0035 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0036 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0037 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0038 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0039 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0040 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0041 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0042 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0043 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0044 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0045 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0046 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0047 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0048 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0049 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0050 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0051 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0052 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0053 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0054 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0055 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0056 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0057 21/8/2025 00:53:43 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0058 21/8/2025 00:53:43 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0059 21/8/2025 00:54:19 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRE84SBJL1',NULL,'SYS','admin','Successfull login','********','005419','W',0,0,NULL,'sys',NULL)
GO
/* #0060 21/8/2025 00:54:19 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:56904 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********005419','9900')
GO
/* #0061 21/8/2025 00:54:19 (1) */
UPDATE hs_logindata SET last_activity_dt='********005419' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0062 21/8/2025 00:54:32 (0) */

UPDATE hs_act_log SET act_code='change' WHERE act_code='edit' AND act_desc IS NOT NULL

GO
/* #0063 21/8/2025 00:54:32 (0) */

ALTER TABLE hs_shared_table ALTER COLUMN obj_id varchar(32) NOT NULL

GO
/* #0064 21/8/2025 00:54:32 (0) */

ALTER TABLE hs_attached_docs ALTER COLUMN f nvarchar(500) NULL

GO
/* #0065 21/8/2025 00:54:32 (0) */



ALTER TABLE hs_act_log ALTER COLUMN obj_type varchar(8) NULL

GO
/* #0066 21/8/2025 00:54:32 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_code varchar(8) NULL

GO
/* #0067 21/8/2025 00:54:32 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_at varchar(14) NULL

GO
/* #0068 21/8/2025 00:54:32 (0) */


UPDATE hs_crncy SET max_rate=9999 WHERE sys_client_id is not null and max_rate > 9999

GO
/* #0069 21/8/2025 00:54:32 (0) */


UPDATE fi_accounts SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0070 21/8/2025 00:54:32 (0) */


UPDATE fi_trans_entry SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0071 21/8/2025 00:54:32 (0) */


UPDATE A SET A.doc_type=B.doc_type, A.doc_no=B.doc_no FROM fi_trans_entry A INNER JOIN fi_trans B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id
WHERE A.doc_type IS NULL AND A.doc_no IS NULL

GO
/* #0072 21/8/2025 00:54:32 (0) */


DROP VIEW fi_vu_trans_entry

GO
/* #0073 21/8/2025 00:54:32 (0) */


CREATE VIEW fi_vu_trans_entry
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, B.cov_amount_debit, B.cov_amount_credit, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.cov_crncy, 
                         B.cov_crncy_exrate, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.sub_gl_no, B.line_ref_no, B.line_ref_date,
                         B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid, B.doc_year + B.doc_month AS doc_ym
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id

GO
/* #0074 21/8/2025 00:54:33 (0) */


DROP VIEW fi_vu_trans_entry_ex

GO
/* #0075 21/8/2025 00:54:33 (0) */


CREATE VIEW fi_vu_trans_entry_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, B.sub_gl_no, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.ma_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0076 21/8/2025 00:54:33 (0) */


DROP VIEW fi_vu_trans_entry_cov_ex

GO
/* #0077 21/8/2025 00:54:33 (0) */


CREATE VIEW fi_vu_trans_entry_cov_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0078 21/8/2025 00:54:33 (0) */





DROP VIEW vu_biz_docs

GO
/* #0079 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_biz_docs
AS
SELECT        sys_client_id, doc_type, doc_no, cash_id AS doc_acc_no, cov_no, price AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         price * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_sales_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, cash_acc_no AS doc_acc_no, cov_no, amount AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         amount * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_fin_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, NULL AS doc_acc_no, NULL AS cov_no, amount AS doc_amount, doc_crncy, entry_memo AS doc_note, doc_status, 
                         suspended, draft, doc_date, ref_doc_type, ref_doc_no, crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, 
                        1 AS doc_crncy_exrate, amount  AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) 
                         AS doc_month
FROM            fi_gl_entries


GO
/* #0080 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_doc_line_items

GO
/* #0081 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_doc_line_items
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) 
                         AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no, C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, 
                         C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, 
                         CAST(ISNULL(A.doc_crncy_exrate, 1) * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, B.ex_data30, B.line_id, ISNULL(B.branch, 
                         A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id, C.branch AS item_branch, C.proj_id AS item_proj
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0082 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_doc_line_items_ex

GO
/* #0083 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_doc_line_items_ex
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no,A.cov_name, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) 
                         AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no,
                        C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 
                         1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, CAST(ISNULL(A.doc_crncy_exrate, 1) 
                         * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, 
                         B.ex_data30, B.line_id, ISNULL(B.branch, A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0084 21/8/2025 00:54:33 (0) */



DROP VIEW vu_es_stock_qty

GO
/* #0085 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_stock_qty
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, B.item_name, A.item_unit, A.batch_no, A.unit_cost, A.item_qty, A.rsrvd_qty, B.item_group, B.for_name, B.sci_name, 
                         B.item_code, B.item_agent, B.item_make
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0086 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_item_units

GO
/* #0087 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_item_units
AS
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u1_id] AS u_id, [u1_price] AS u_price, 
                         [u1_cost] AS u_cost, 1.0 AS u_in_u1, 1 AS u_x
FROM            [es_items]
WHERE        u1_id IS NOT NULL
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u2_id] AS u_id, [u2_price] AS u_price, 
                         [u2_cost] AS u_cost, 1.0 / u1_to_u2 AS u_in_u1, 2 AS u_x
FROM            [es_items]
WHERE        u1_to_u2 > 0
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u3_id] AS u_id, [u3_price] AS u_price, 
                         [u3_cost] AS u_cost, 1.0 / u1_to_u2 / u2_to_u3 AS u_in_u1, 3 AS u_x
FROM            [es_items]
WHERE         u1_to_u2 > 0 AND  u2_to_u3 > 0


GO
/* #0088 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_stock_stats

GO
/* #0089 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_stock_stats
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.item_qty, A.rsrvd_qty, B.item_type, B.item_group, B.item_subgr, B.item_form, B.item_make, 
                         B.item_agent, B.u_price, B.u_cost, B.u1_id, B.u_in_u1, B.u_x, A.item_qty * B.u_in_u1 AS item_qty_u1
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.vu_es_item_units AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id AND A.item_unit = B.u_id
WHERE        (A.item_qty <> 0)


GO
/* #0090 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_doc_line_items_net_sales

GO
/* #0091 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_doc_line_items_net_sales
AS
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], [item_qty1], [item_qty2], [item_discount], [sub_total], [sub_total_cc], 
                         [item_qty_u1], [free_qty_u1], [u1_id], [profit_cc], [from_date], [to_date], [line_status], ref_doc_type, ref_doc_no, [item_qty1] AS item_qty1_sold, 0 AS item_qty1_ret, [item_qty_u1] AS item_qty_u1_sold, 0 AS item_qty_u1_ret, 
                         [sub_total_cc] AS sub_total_cc_sold, 0 AS sub_total_cc_ret, ISNULL([profit_cc], 0) AS profit_cc_sold, 0 AS profit_cc_ret,sub_total AS sub_total_sold, 0 AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '201' OR doc_type = '203' OR doc_type='401'
UNION
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], - 1 * [item_qty1] AS item_qty1, - 1 * [item_qty2] AS item_qty2, 
                         [item_discount], - 1 * [sub_total] AS sub_total, - 1 * [sub_total_cc] AS sub_total_cc, - 1 * [item_qty_u1] AS item_qty_u1, - 1 * ISNULL([free_qty_u1], 0) AS free_qty_u1, [u1_id], - 1 * ISNULL([profit_cc], 0) AS profit_cc, [from_date], 
                         [to_date], [line_status], ref_doc_type, ref_doc_no, 0 AS item_qty1_sold, [item_qty1] AS item_qty1_ret, 0 AS item_qty_u1_sold, [item_qty_u1] AS item_qty_u1_ret, 0 AS sub_total_cc_sold, [sub_total_cc] AS sub_total_cc_ret, 
                         0 AS profit_cc_sold, ISNULL([profit_cc], 0) AS profit_cc_ret,0 AS sub_total_sold, [sub_total] AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '202'


GO
/* #0092 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_stock_trans

GO
/* #0093 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_stock_trans AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, A.tr_time, A.u1_id, A.u1_cost, A.u1_ave_cost, 
                         A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date
FROM            es_stock_trans AS A LEFT OUTER JOIN
                         dbo.es_sales_docs AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no

GO
/* #0094 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_stock_trans_price

GO
/* #0095 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_stock_trans_price
AS
SELECT        B.unit_price, B.doc_crncy, A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, 
                         A.tr_time, A.u1_id, A.u1_cost,A.u1_ave_cost, A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date, B.doc_year, B.doc_month, B.unit_cost
FROM            dbo.es_stock_trans AS A LEFT OUTER JOIN
                         dbo.vu_es_doc_line_items AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.line_id = B.line_id AND A.batch_no = ISNULL(B.batch_no, N'0')

GO
/* #0096 21/8/2025 00:54:33 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN phone varchar(20) NULL

GO
/* #0097 21/8/2025 00:54:33 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN address nvarchar(100) NULL

GO
/* #0098 21/8/2025 00:54:33 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN to_date varchar(10) NULL

GO
/* #0099 21/8/2025 00:54:33 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN from_date varchar(10) NULL

GO
/* #0100 21/8/2025 00:54:33 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN warranty nvarchar(100) NULL

GO
/* #0101 21/8/2025 00:54:33 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units]

GO
/* #0102 21/8/2025 00:54:33 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units]
AS
select sys_client_id, store_id,item_id,batch_no, [1] as u1_qty,[2] as u2_qty,[3] as u3_qty
from
(
  select sys_client_id, store_id,item_id,batch_no, item_qty, u_x
  from vu_es_stock_stats
) t
pivot
(
  SUM(item_qty) for u_x in ([1],[2],[3])
) pvt


GO
/* #0103 21/8/2025 00:54:33 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units_details]

GO
/* #0104 21/8/2025 00:54:33 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units_details]
AS
SELECT  A.sys_client_id, A.store_id, A.item_id, A.batch_no, A.u1_qty, A.u2_qty, A.u3_qty, B.u1_id, B.u1_price, B.u1_cost, B.u2_id, B.u2_cost, B.u2_price, B.u3_id, 
                         B.u3_cost, B.u3_price, B.item_group, B.item_name
FROM            dbo.vu_es_stock_qty_pvt_units AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0105 21/8/2025 00:54:33 (0) */


UPDATE A SET A.line_id=B.line_id FROM dbo.es_stock_trans A 
INNER JOIN  es_doc_details B
ON A.line_id IS NULL AND A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.batch_no = ISNULL(B.batch_no, N'0')


GO
/* #0106 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_items_cache

GO
/* #0107 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_items_cache
AS
SELECT        A.item_id, A.item_name, A.sci_name, A.item_code, A.for_name, A.item_status, A.item_type, A.batch_type, A.item_group, A.def_purch_unit, A.def_sale_unit, 
                         A.item_crncy, A.u1_id, A.u1_price, A.u1_cost, A.u1_min_price, A.u2_id, A.u2_price, A.u2_cost, A.u2_min_price, A.u3_id, A.u3_price, A.u3_cost, A.u3_min_price, 
                         A.item_spec, A.item_tax_pct, A.sys_client_id, A.item_flags, B.item_qty_u1, 
                         A.u1_price_s1, A.u1_price_s2, A.u1_price_s3, A.u2_price_s1, A.u2_price_s2, A.u2_price_s3, A.u3_price_s1, A.u3_price_s2, A.u3_price_s3
FROM            dbo.es_items AS A LEFT OUTER JOIN
                             (SELECT sys_client_id, item_id, u1_id, SUM(item_qty_u1) AS item_qty_u1, SUM(item_qty * u_cost) AS u_cost
                                FROM dbo.vu_es_stock_stats
                                GROUP BY sys_client_id, item_id, u1_id) AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id

GO
/* #0108 21/8/2025 00:54:33 (0) */



DROP VIEW vu_es_booking_sched

GO
/* #0109 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_booking_sched
AS
SELECT A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.cov_name, A.address, A.phone, A.from_date, A.to_date, A.net_amount, A.paid_installs, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, 
                         SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.crtd_by, A.crtd_time, B.item_id, B.item_unit, B.batch_no, B.book_status, B.book_date, 
                         B.item_qty, B.is_sub, C.item_group, A.rem_amount, A.paid, A.pnet_amount
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_booking_sched AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0110 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_cash_docs

GO
/* #0111 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_cash_docs
AS
SELECT sys_client_id, doc_type, doc_no, doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, paid AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND paid > 0
UNION
SELECT sys_client_id, doc_type, doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, pnet_amount AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND pnet_amount > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,1 as cash_dir, pay_type, cash_acc_no AS doc_acc_no, amount AS in_amnt, 0 AS out_amnt
FROM  es_fin_docs WHERE doc_type='101'
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,2 as cash_dir, pay_type, cash_acc_no AS doc_acc_no,0 AS in_amnt, amount AS out_amnt
FROM  es_fin_docs WHERE doc_type='102' 
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, 0 AS in_amnt, paid AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND paid > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, 0 AS in_amnt, pnet_amount AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND pnet_amount > 0


GO
/* #0112 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_actual_sales_docs

GO
/* #0113 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_actual_sales_docs
AS
SELECT *, net_amount * ISNULL(doc_crncy_exrate, 1) AS net_amount_cc FROM es_sales_docs WHERE (doc_type = '201') OR (doc_type = '203') OR (doc_type = '206') OR  (doc_type = '208') OR (doc_type = '401')

GO
/* #0114 21/8/2025 00:54:33 (0) */


DROP VIEW vu_es_net_sales_docs

GO
/* #0115 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_es_net_sales_docs
AS
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, net_amount,net_amount as sales_amount, 0 as ret_amount
FROM es_sales_docs WHERE  (doc_type = '201') OR  (doc_type = '203') OR (doc_type = '206') OR (doc_type = '208') OR (doc_type = '401')  
UNION
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, -net_amount, 0 as sales_amount, -net_amount as ret_amount
FROM es_sales_docs WHERE  (doc_type = '202')

GO
/* #0116 21/8/2025 00:54:33 (0) */


DROP VIEW fi_vu_trans_entry_sd

GO
/* #0117 21/8/2025 00:54:33 (0) */


CREATE VIEW fi_vu_trans_entry_sd
AS
SELECT B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, B.amount_debit - B.amount_credit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, 
                  B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, B.amount_credit_cc, B.amount_debit_cc - B.amount_credit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, B.proj_id, B.actvty_id, 
                  B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar, 
                  B.tr_entry_no) + '-' + CONVERT(varchar, B.entry_line_no) AS line_uid, C.cust_no, C.cust_grp, C.region, C.sales_rep
FROM     dbo.fi_trans AS A RIGHT OUTER JOIN
                  dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id 
                  INNER JOIN
                  dbo.es_cust AS C ON B.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.cust_no

GO
/* #0118 21/8/2025 00:54:33 (0) */


DROP INDEX fi_trans_doc_data_index ON fi_trans_entry

GO
/* #0119 21/8/2025 00:54:33 (0) */

CREATE NONCLUSTERED INDEX fi_trans_doc_data_index ON fi_trans_entry (doc_type ASC,doc_no ASC) 

GO
/* #0120 21/8/2025 00:54:33 (0) */


DROP INDEX es_sales_docs_date_index ON es_sales_docs

GO
/* #0121 21/8/2025 00:54:33 (0) */


CREATE INDEX es_sales_docs_date_index ON es_sales_docs (doc_date ASC)

GO
/* #0122 21/8/2025 00:54:33 (0) */



DROP VIEW vu_hr_clock

GO
/* #0123 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_hr_clock AS
SELECT A.*, emp_title,branch,emp_dept,emp_section,emp_ou FROM hr_clock A
LEFT JOIN hr_emp_data B
ON
A.sys_client_id=B.sys_client_id AND A.emp_no=B.emp_no

GO
/* #0124 21/8/2025 00:54:33 (0) */


DROP VIEW vu_hr_ts_doc_details

GO
/* #0125 21/8/2025 00:54:33 (0) */


CREATE VIEW vu_hr_ts_doc_details AS
SELECT A.*, doc_status FROM  hr_ts_doc_details A
LEFT JOIN hr_ts_docs B
ON
A.sys_client_id=B.sys_client_id AND A.doc_type=B.doc_type AND A.doc_no = B.doc_no

GO
/* #0126 21/8/2025 00:54:48 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0001 21/8/2025 00:54:57 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0002 21/8/2025 00:54:57 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0003 21/8/2025 00:54:57 (1) */
UPDATE hs_config SET cfg_value='l7+tuqeVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0004 21/8/2025 00:54:57 (1) */
UPDATE hs_config SET cfg_value='mKGv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0005 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0006 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0007 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0008 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0009 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0010 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0011 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0012 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0013 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0014 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0015 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0016 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0017 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0018 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0019 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0020 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0021 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0022 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0023 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0024 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0025 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0026 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0027 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0028 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0029 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0030 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0031 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0032 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0033 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0034 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0035 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0036 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0037 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0038 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0039 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0040 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0041 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0042 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0043 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0044 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0045 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0046 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0047 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0048 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0049 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0050 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0051 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0052 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0053 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0054 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0055 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0056 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0057 21/8/2025 00:54:57 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0058 21/8/2025 00:54:57 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0001 21/8/2025 00:55:04 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0002 21/8/2025 00:55:04 (1) */
UPDATE hs_config SET cfg_value='k7qsuqeVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0003 21/8/2025 00:55:04 (1) */
UPDATE hs_config SET cfg_value='maGv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0004 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0005 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0006 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0007 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0008 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0009 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0010 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0011 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0012 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0013 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0014 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0015 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0016 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0017 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0018 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0019 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0020 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0021 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0022 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0023 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0024 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0025 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0026 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0027 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0028 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0029 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0030 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0031 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0032 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0033 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0034 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0035 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0036 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0037 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0038 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0039 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0040 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0041 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0042 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0043 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0044 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0045 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0046 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0047 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0048 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0049 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0050 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0051 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0052 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0053 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0054 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0055 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0056 21/8/2025 00:55:04 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0057 21/8/2025 00:55:04 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0058 21/8/2025 00:55:06 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JREFY6YMT1',NULL,'SYS','admin','Successfull login','********','005506','W',0,0,NULL,'sys',NULL)
GO
/* #0059 21/8/2025 00:55:10 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:56904 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********005506','9900')
GO
/* #0060 21/8/2025 00:55:10 (1) */
UPDATE hs_logindata SET last_activity_dt='********005506' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0061 21/8/2025 00:55:54 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','logout','http IP=::1:56904 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********005554','9900')
GO
/* #0062 21/8/2025 00:56:10 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('sys',NULL,'x-shutdown','alert',' @: Last shutdown was not clean, check the system','********005610','9900')
GO
/* #0063 21/8/2025 00:56:14 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRER6K6E9',NULL,'SYS','admin','Successfull login','********','005614','W',0,0,NULL,'sys',NULL)
GO
/* #0064 21/8/2025 00:56:14 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:57282 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********005614','9900')
GO
/* #0065 21/8/2025 00:56:14 (1) */
UPDATE hs_logindata SET last_activity_dt='********005614' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0066 21/8/2025 00:56:55 (14) */
DELETE FROM hs_relations WHERE sys_client_id='9900' AND rel_type='USRL' AND parent_id='admin'
GO
/* #0067 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','all_admin','9900')
GO
/* #0068 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','app_adm','9900')
GO
/* #0069 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','app_dev','9900')
GO
/* #0070 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','usr_adm','9900')
GO
/* #0071 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-user','9900')
GO
/* #0072 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-admin','9900')
GO
/* #0073 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-so-mem','9900')
GO
/* #0074 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-so-vend','9900')
GO
/* #0075 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-so-trns','9900')
GO
/* #0076 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','es-so-admn','9900')
GO
/* #0077 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','RL000001','9900')
GO
/* #0078 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','RL000002','9900')
GO
/* #0079 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','RL000003','9900')
GO
/* #0080 21/8/2025 00:56:55 (1) */
INSERT INTO hs_relations (rel_type,parent_id,child_id,sys_client_id) VALUES ('USRL','admin','RL000004','9900')
GO
/* #0081 21/8/2025 00:56:55 (1) */
UPDATE hs_logindata SET user_name = 'متجر المتاجر - A STORE THE STORE',user_status = '1',valid_from = NULL,valid_to = NULL,user_group = NULL,user_subgroup = NULL,user_flags = 16,fi_acc_no = NULL,sys_client_id = '9900'  WHERE ( (sys_client_id = '9900') AND ((user_group IS NULL) ) ) AND ( user_id = 'admin' )
GO
/* #0082 21/8/2025 00:56:55 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('user','admin','admin','change','user_flags : 0 => 16
user_roles : all_admin;app_adm;app_dev;es-admin;es-so-admn;es-so-mem;es-so-trns;es-so-vend;es-user;RL000001;RL000002;RL000003;RL000004;usr_adm; => all_admin;app_adm;app_dev;usr_adm;es-user;es-admin;es-so-mem;es-so-vend;es-so-trns;es-','********005655','9900')
GO
/* #0083 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='user_hp'
GO
/* #0084 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='user_theme'
GO
/* #0085 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='user_ips'
GO
/* #0086 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='user_machs'
GO
/* #0087 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value='cms;fi-gl;es-stock;es-procure;hcm-sys;hcm-ess;sstore;ss-vend;ss-trns;' WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='user_menus'
GO
/* #0088 21/8/2025 00:57:58 (1) */
INSERT INTO hs_obj_attribs (obj_type,obj_id,attr_key,attr_value,sys_client_id) VALUES ('user','guest','user_menus','cms;fi-gl;es-stock;es-procure;hcm-sys;hcm-ess;sstore;ss-vend;ss-trns;','9900')
GO
/* #0089 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_cash_id'
GO
/* #0090 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_bank_id'
GO
/* #0091 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_branch_id'
GO
/* #0092 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_proj_id'
GO
/* #0093 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_sales_rep'
GO
/* #0094 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_sales_region'
GO
/* #0095 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_prod_line'
GO
/* #0096 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_branch'
GO
/* #0097 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_teller'
GO
/* #0098 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_banks'
GO
/* #0099 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_cc'
GO
/* #0100 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_proj'
GO
/* #0101 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_actv'
GO
/* #0102 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_accs'
GO
/* #0103 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='max_discount_pct'
GO
/* #0104 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_store_id'
GO
/* #0105 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_sales'
GO
/* #0106 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='auth_usr_store'
GO
/* #0107 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='usr_item_grps'
GO
/* #0108 21/8/2025 00:57:58 (0) */
UPDATE hs_obj_attribs SET attr_value=NULL WHERE sys_client_id='9900' AND obj_type='user' AND obj_id='guest' AND attr_key='def_item_coll'
GO
/* #0109 21/8/2025 00:57:58 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('user','admin','guest','change','user_menus :  => cms;fi-gl;es-stock;es-procure;hcm-sys;hcm-ess;sstore;ss-vend;ss-trns;
','********005758','9900')
GO
/* #0110 21/8/2025 00:57:58 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('user','admin','guest','edit',NULL,'********005758','9900')
GO
/* #0111 21/8/2025 00:59:09 (0) */

UPDATE hs_act_log SET act_code='change' WHERE act_code='edit' AND act_desc IS NOT NULL

GO
/* #0112 21/8/2025 00:59:09 (0) */

ALTER TABLE hs_shared_table ALTER COLUMN obj_id varchar(32) NOT NULL

GO
/* #0113 21/8/2025 00:59:09 (0) */

ALTER TABLE hs_attached_docs ALTER COLUMN f nvarchar(500) NULL

GO
/* #0114 21/8/2025 00:59:09 (0) */



ALTER TABLE hs_act_log ALTER COLUMN obj_type varchar(8) NULL

GO
/* #0115 21/8/2025 00:59:09 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_code varchar(8) NULL

GO
/* #0116 21/8/2025 00:59:09 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_at varchar(14) NULL

GO
/* #0117 21/8/2025 00:59:09 (0) */


UPDATE hs_crncy SET max_rate=9999 WHERE sys_client_id is not null and max_rate > 9999

GO
/* #0118 21/8/2025 00:59:09 (0) */


UPDATE fi_accounts SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0119 21/8/2025 00:59:09 (0) */


UPDATE fi_trans_entry SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0120 21/8/2025 00:59:09 (0) */


UPDATE A SET A.doc_type=B.doc_type, A.doc_no=B.doc_no FROM fi_trans_entry A INNER JOIN fi_trans B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id
WHERE A.doc_type IS NULL AND A.doc_no IS NULL

GO
/* #0121 21/8/2025 00:59:09 (0) */


DROP VIEW fi_vu_trans_entry

GO
/* #0122 21/8/2025 00:59:09 (0) */


CREATE VIEW fi_vu_trans_entry
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, B.cov_amount_debit, B.cov_amount_credit, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.cov_crncy, 
                         B.cov_crncy_exrate, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.sub_gl_no, B.line_ref_no, B.line_ref_date,
                         B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid, B.doc_year + B.doc_month AS doc_ym
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id

GO
/* #0123 21/8/2025 00:59:09 (0) */


DROP VIEW fi_vu_trans_entry_ex

GO
/* #0124 21/8/2025 00:59:09 (0) */


CREATE VIEW fi_vu_trans_entry_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, B.sub_gl_no, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.ma_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0125 21/8/2025 00:59:09 (0) */


DROP VIEW fi_vu_trans_entry_cov_ex

GO
/* #0126 21/8/2025 00:59:09 (0) */


CREATE VIEW fi_vu_trans_entry_cov_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0127 21/8/2025 00:59:09 (0) */





DROP VIEW vu_biz_docs

GO
/* #0128 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_biz_docs
AS
SELECT        sys_client_id, doc_type, doc_no, cash_id AS doc_acc_no, cov_no, price AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         price * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_sales_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, cash_acc_no AS doc_acc_no, cov_no, amount AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         amount * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_fin_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, NULL AS doc_acc_no, NULL AS cov_no, amount AS doc_amount, doc_crncy, entry_memo AS doc_note, doc_status, 
                         suspended, draft, doc_date, ref_doc_type, ref_doc_no, crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, 
                        1 AS doc_crncy_exrate, amount  AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) 
                         AS doc_month
FROM            fi_gl_entries


GO
/* #0129 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_doc_line_items

GO
/* #0130 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_doc_line_items
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) 
                         AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no, C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, 
                         C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, 
                         CAST(ISNULL(A.doc_crncy_exrate, 1) * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, B.ex_data30, B.line_id, ISNULL(B.branch, 
                         A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id, C.branch AS item_branch, C.proj_id AS item_proj
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0131 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_doc_line_items_ex

GO
/* #0132 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_doc_line_items_ex
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no,A.cov_name, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) 
                         AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no,
                        C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 
                         1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, CAST(ISNULL(A.doc_crncy_exrate, 1) 
                         * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, 
                         B.ex_data30, B.line_id, ISNULL(B.branch, A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0133 21/8/2025 00:59:09 (0) */



DROP VIEW vu_es_stock_qty

GO
/* #0134 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_stock_qty
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, B.item_name, A.item_unit, A.batch_no, A.unit_cost, A.item_qty, A.rsrvd_qty, B.item_group, B.for_name, B.sci_name, 
                         B.item_code, B.item_agent, B.item_make
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0135 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_item_units

GO
/* #0136 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_item_units
AS
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u1_id] AS u_id, [u1_price] AS u_price, 
                         [u1_cost] AS u_cost, 1.0 AS u_in_u1, 1 AS u_x
FROM            [es_items]
WHERE        u1_id IS NOT NULL
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u2_id] AS u_id, [u2_price] AS u_price, 
                         [u2_cost] AS u_cost, 1.0 / u1_to_u2 AS u_in_u1, 2 AS u_x
FROM            [es_items]
WHERE        u1_to_u2 > 0
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u3_id] AS u_id, [u3_price] AS u_price, 
                         [u3_cost] AS u_cost, 1.0 / u1_to_u2 / u2_to_u3 AS u_in_u1, 3 AS u_x
FROM            [es_items]
WHERE         u1_to_u2 > 0 AND  u2_to_u3 > 0


GO
/* #0137 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_stock_stats

GO
/* #0138 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_stock_stats
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.item_qty, A.rsrvd_qty, B.item_type, B.item_group, B.item_subgr, B.item_form, B.item_make, 
                         B.item_agent, B.u_price, B.u_cost, B.u1_id, B.u_in_u1, B.u_x, A.item_qty * B.u_in_u1 AS item_qty_u1
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.vu_es_item_units AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id AND A.item_unit = B.u_id
WHERE        (A.item_qty <> 0)


GO
/* #0139 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_doc_line_items_net_sales

GO
/* #0140 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_doc_line_items_net_sales
AS
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], [item_qty1], [item_qty2], [item_discount], [sub_total], [sub_total_cc], 
                         [item_qty_u1], [free_qty_u1], [u1_id], [profit_cc], [from_date], [to_date], [line_status], ref_doc_type, ref_doc_no, [item_qty1] AS item_qty1_sold, 0 AS item_qty1_ret, [item_qty_u1] AS item_qty_u1_sold, 0 AS item_qty_u1_ret, 
                         [sub_total_cc] AS sub_total_cc_sold, 0 AS sub_total_cc_ret, ISNULL([profit_cc], 0) AS profit_cc_sold, 0 AS profit_cc_ret,sub_total AS sub_total_sold, 0 AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '201' OR doc_type = '203' OR doc_type='401'
UNION
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], - 1 * [item_qty1] AS item_qty1, - 1 * [item_qty2] AS item_qty2, 
                         [item_discount], - 1 * [sub_total] AS sub_total, - 1 * [sub_total_cc] AS sub_total_cc, - 1 * [item_qty_u1] AS item_qty_u1, - 1 * ISNULL([free_qty_u1], 0) AS free_qty_u1, [u1_id], - 1 * ISNULL([profit_cc], 0) AS profit_cc, [from_date], 
                         [to_date], [line_status], ref_doc_type, ref_doc_no, 0 AS item_qty1_sold, [item_qty1] AS item_qty1_ret, 0 AS item_qty_u1_sold, [item_qty_u1] AS item_qty_u1_ret, 0 AS sub_total_cc_sold, [sub_total_cc] AS sub_total_cc_ret, 
                         0 AS profit_cc_sold, ISNULL([profit_cc], 0) AS profit_cc_ret,0 AS sub_total_sold, [sub_total] AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '202'


GO
/* #0141 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_stock_trans

GO
/* #0142 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_stock_trans AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, A.tr_time, A.u1_id, A.u1_cost, A.u1_ave_cost, 
                         A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date
FROM            es_stock_trans AS A LEFT OUTER JOIN
                         dbo.es_sales_docs AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no

GO
/* #0143 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_stock_trans_price

GO
/* #0144 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_stock_trans_price
AS
SELECT        B.unit_price, B.doc_crncy, A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, 
                         A.tr_time, A.u1_id, A.u1_cost,A.u1_ave_cost, A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date, B.doc_year, B.doc_month, B.unit_cost
FROM            dbo.es_stock_trans AS A LEFT OUTER JOIN
                         dbo.vu_es_doc_line_items AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.line_id = B.line_id AND A.batch_no = ISNULL(B.batch_no, N'0')

GO
/* #0145 21/8/2025 00:59:09 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN phone varchar(20) NULL

GO
/* #0146 21/8/2025 00:59:09 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN address nvarchar(100) NULL

GO
/* #0147 21/8/2025 00:59:09 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN to_date varchar(10) NULL

GO
/* #0148 21/8/2025 00:59:09 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN from_date varchar(10) NULL

GO
/* #0149 21/8/2025 00:59:09 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN warranty nvarchar(100) NULL

GO
/* #0150 21/8/2025 00:59:09 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units]

GO
/* #0151 21/8/2025 00:59:09 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units]
AS
select sys_client_id, store_id,item_id,batch_no, [1] as u1_qty,[2] as u2_qty,[3] as u3_qty
from
(
  select sys_client_id, store_id,item_id,batch_no, item_qty, u_x
  from vu_es_stock_stats
) t
pivot
(
  SUM(item_qty) for u_x in ([1],[2],[3])
) pvt


GO
/* #0152 21/8/2025 00:59:09 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units_details]

GO
/* #0153 21/8/2025 00:59:09 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units_details]
AS
SELECT  A.sys_client_id, A.store_id, A.item_id, A.batch_no, A.u1_qty, A.u2_qty, A.u3_qty, B.u1_id, B.u1_price, B.u1_cost, B.u2_id, B.u2_cost, B.u2_price, B.u3_id, 
                         B.u3_cost, B.u3_price, B.item_group, B.item_name
FROM            dbo.vu_es_stock_qty_pvt_units AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0154 21/8/2025 00:59:09 (0) */


UPDATE A SET A.line_id=B.line_id FROM dbo.es_stock_trans A 
INNER JOIN  es_doc_details B
ON A.line_id IS NULL AND A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.batch_no = ISNULL(B.batch_no, N'0')


GO
/* #0155 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_items_cache

GO
/* #0156 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_items_cache
AS
SELECT        A.item_id, A.item_name, A.sci_name, A.item_code, A.for_name, A.item_status, A.item_type, A.batch_type, A.item_group, A.def_purch_unit, A.def_sale_unit, 
                         A.item_crncy, A.u1_id, A.u1_price, A.u1_cost, A.u1_min_price, A.u2_id, A.u2_price, A.u2_cost, A.u2_min_price, A.u3_id, A.u3_price, A.u3_cost, A.u3_min_price, 
                         A.item_spec, A.item_tax_pct, A.sys_client_id, A.item_flags, B.item_qty_u1, 
                         A.u1_price_s1, A.u1_price_s2, A.u1_price_s3, A.u2_price_s1, A.u2_price_s2, A.u2_price_s3, A.u3_price_s1, A.u3_price_s2, A.u3_price_s3
FROM            dbo.es_items AS A LEFT OUTER JOIN
                             (SELECT sys_client_id, item_id, u1_id, SUM(item_qty_u1) AS item_qty_u1, SUM(item_qty * u_cost) AS u_cost
                                FROM dbo.vu_es_stock_stats
                                GROUP BY sys_client_id, item_id, u1_id) AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id

GO
/* #0157 21/8/2025 00:59:09 (0) */



DROP VIEW vu_es_booking_sched

GO
/* #0158 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_booking_sched
AS
SELECT A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.cov_name, A.address, A.phone, A.from_date, A.to_date, A.net_amount, A.paid_installs, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, 
                         SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.crtd_by, A.crtd_time, B.item_id, B.item_unit, B.batch_no, B.book_status, B.book_date, 
                         B.item_qty, B.is_sub, C.item_group, A.rem_amount, A.paid, A.pnet_amount
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_booking_sched AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0159 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_cash_docs

GO
/* #0160 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_cash_docs
AS
SELECT sys_client_id, doc_type, doc_no, doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, paid AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND paid > 0
UNION
SELECT sys_client_id, doc_type, doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, pnet_amount AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND pnet_amount > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,1 as cash_dir, pay_type, cash_acc_no AS doc_acc_no, amount AS in_amnt, 0 AS out_amnt
FROM  es_fin_docs WHERE doc_type='101'
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,2 as cash_dir, pay_type, cash_acc_no AS doc_acc_no,0 AS in_amnt, amount AS out_amnt
FROM  es_fin_docs WHERE doc_type='102' 
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, 0 AS in_amnt, paid AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND paid > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, 0 AS in_amnt, pnet_amount AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND pnet_amount > 0


GO
/* #0161 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_actual_sales_docs

GO
/* #0162 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_actual_sales_docs
AS
SELECT *, net_amount * ISNULL(doc_crncy_exrate, 1) AS net_amount_cc FROM es_sales_docs WHERE (doc_type = '201') OR (doc_type = '203') OR (doc_type = '206') OR  (doc_type = '208') OR (doc_type = '401')

GO
/* #0163 21/8/2025 00:59:09 (0) */


DROP VIEW vu_es_net_sales_docs

GO
/* #0164 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_es_net_sales_docs
AS
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, net_amount,net_amount as sales_amount, 0 as ret_amount
FROM es_sales_docs WHERE  (doc_type = '201') OR  (doc_type = '203') OR (doc_type = '206') OR (doc_type = '208') OR (doc_type = '401')  
UNION
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, -net_amount, 0 as sales_amount, -net_amount as ret_amount
FROM es_sales_docs WHERE  (doc_type = '202')

GO
/* #0165 21/8/2025 00:59:09 (0) */


DROP VIEW fi_vu_trans_entry_sd

GO
/* #0166 21/8/2025 00:59:09 (0) */


CREATE VIEW fi_vu_trans_entry_sd
AS
SELECT B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, B.amount_debit - B.amount_credit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, 
                  B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, B.amount_credit_cc, B.amount_debit_cc - B.amount_credit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, B.proj_id, B.actvty_id, 
                  B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar, 
                  B.tr_entry_no) + '-' + CONVERT(varchar, B.entry_line_no) AS line_uid, C.cust_no, C.cust_grp, C.region, C.sales_rep
FROM     dbo.fi_trans AS A RIGHT OUTER JOIN
                  dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id 
                  INNER JOIN
                  dbo.es_cust AS C ON B.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.cust_no

GO
/* #0167 21/8/2025 00:59:09 (0) */


DROP INDEX fi_trans_doc_data_index ON fi_trans_entry

GO
/* #0168 21/8/2025 00:59:09 (0) */

CREATE NONCLUSTERED INDEX fi_trans_doc_data_index ON fi_trans_entry (doc_type ASC,doc_no ASC) 

GO
/* #0169 21/8/2025 00:59:09 (0) */


DROP INDEX es_sales_docs_date_index ON es_sales_docs

GO
/* #0170 21/8/2025 00:59:09 (0) */


CREATE INDEX es_sales_docs_date_index ON es_sales_docs (doc_date ASC)

GO
/* #0171 21/8/2025 00:59:09 (0) */



DROP VIEW vu_hr_clock

GO
/* #0172 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_hr_clock AS
SELECT A.*, emp_title,branch,emp_dept,emp_section,emp_ou FROM hr_clock A
LEFT JOIN hr_emp_data B
ON
A.sys_client_id=B.sys_client_id AND A.emp_no=B.emp_no

GO
/* #0173 21/8/2025 00:59:09 (0) */


DROP VIEW vu_hr_ts_doc_details

GO
/* #0174 21/8/2025 00:59:09 (0) */


CREATE VIEW vu_hr_ts_doc_details AS
SELECT A.*, doc_status FROM  hr_ts_doc_details A
LEFT JOIN hr_ts_docs B
ON
A.sys_client_id=B.sys_client_id AND A.doc_type=B.doc_type AND A.doc_no = B.doc_no

GO
/* #0175 21/8/2025 00:59:42 (0) */

UPDATE hs_act_log SET act_code='change' WHERE act_code='edit' AND act_desc IS NOT NULL

GO
/* #0176 21/8/2025 00:59:42 (0) */

ALTER TABLE hs_shared_table ALTER COLUMN obj_id varchar(32) NOT NULL

GO
/* #0177 21/8/2025 00:59:42 (0) */

ALTER TABLE hs_attached_docs ALTER COLUMN f nvarchar(500) NULL

GO
/* #0178 21/8/2025 00:59:42 (0) */



ALTER TABLE hs_act_log ALTER COLUMN obj_type varchar(8) NULL

GO
/* #0179 21/8/2025 00:59:42 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_code varchar(8) NULL

GO
/* #0180 21/8/2025 00:59:42 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_at varchar(14) NULL

GO
/* #0181 21/8/2025 00:59:42 (0) */


UPDATE hs_crncy SET max_rate=9999 WHERE sys_client_id is not null and max_rate > 9999

GO
/* #0182 21/8/2025 00:59:42 (0) */


UPDATE fi_accounts SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0183 21/8/2025 00:59:42 (0) */


UPDATE fi_trans_entry SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0184 21/8/2025 00:59:42 (0) */


UPDATE A SET A.doc_type=B.doc_type, A.doc_no=B.doc_no FROM fi_trans_entry A INNER JOIN fi_trans B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id
WHERE A.doc_type IS NULL AND A.doc_no IS NULL

GO
/* #0185 21/8/2025 00:59:42 (0) */


DROP VIEW fi_vu_trans_entry

GO
/* #0186 21/8/2025 00:59:42 (0) */


CREATE VIEW fi_vu_trans_entry
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, B.cov_amount_debit, B.cov_amount_credit, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.cov_crncy, 
                         B.cov_crncy_exrate, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.sub_gl_no, B.line_ref_no, B.line_ref_date,
                         B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid, B.doc_year + B.doc_month AS doc_ym
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id

GO
/* #0187 21/8/2025 00:59:42 (0) */


DROP VIEW fi_vu_trans_entry_ex

GO
/* #0188 21/8/2025 00:59:42 (0) */


CREATE VIEW fi_vu_trans_entry_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, B.sub_gl_no, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.ma_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0189 21/8/2025 00:59:42 (0) */


DROP VIEW fi_vu_trans_entry_cov_ex

GO
/* #0190 21/8/2025 00:59:42 (0) */


CREATE VIEW fi_vu_trans_entry_cov_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0191 21/8/2025 00:59:42 (0) */





DROP VIEW vu_biz_docs

GO
/* #0192 21/8/2025 00:59:42 (0) */


CREATE VIEW vu_biz_docs
AS
SELECT        sys_client_id, doc_type, doc_no, cash_id AS doc_acc_no, cov_no, price AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         price * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_sales_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, cash_acc_no AS doc_acc_no, cov_no, amount AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         amount * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_fin_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, NULL AS doc_acc_no, NULL AS cov_no, amount AS doc_amount, doc_crncy, entry_memo AS doc_note, doc_status, 
                         suspended, draft, doc_date, ref_doc_type, ref_doc_no, crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, 
                        1 AS doc_crncy_exrate, amount  AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) 
                         AS doc_month
FROM            fi_gl_entries


GO
/* #0193 21/8/2025 00:59:42 (0) */


DROP VIEW vu_es_doc_line_items

GO
/* #0194 21/8/2025 00:59:42 (0) */


CREATE VIEW vu_es_doc_line_items
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) 
                         AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no, C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, 
                         C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, 
                         CAST(ISNULL(A.doc_crncy_exrate, 1) * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, B.ex_data30, B.line_id, ISNULL(B.branch, 
                         A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id, C.branch AS item_branch, C.proj_id AS item_proj
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0195 21/8/2025 00:59:42 (0) */


DROP VIEW vu_es_doc_line_items_ex

GO
/* #0196 21/8/2025 00:59:42 (0) */


CREATE VIEW vu_es_doc_line_items_ex
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no,A.cov_name, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) 
                         AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no,
                        C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 
                         1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, CAST(ISNULL(A.doc_crncy_exrate, 1) 
                         * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, 
                         B.ex_data30, B.line_id, ISNULL(B.branch, A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0197 21/8/2025 00:59:42 (0) */



DROP VIEW vu_es_stock_qty

GO
/* #0198 21/8/2025 00:59:42 (0) */


CREATE VIEW vu_es_stock_qty
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, B.item_name, A.item_unit, A.batch_no, A.unit_cost, A.item_qty, A.rsrvd_qty, B.item_group, B.for_name, B.sci_name, 
                         B.item_code, B.item_agent, B.item_make
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0199 21/8/2025 00:59:42 (0) */


DROP VIEW vu_es_item_units

GO
/* #0200 21/8/2025 00:59:42 (0) */


CREATE VIEW vu_es_item_units
AS
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u1_id] AS u_id, [u1_price] AS u_price, 
                         [u1_cost] AS u_cost, 1.0 AS u_in_u1, 1 AS u_x
FROM            [es_items]
WHERE        u1_id IS NOT NULL
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u2_id] AS u_id, [u2_price] AS u_price, 
                         [u2_cost] AS u_cost, 1.0 / u1_to_u2 AS u_in_u1, 2 AS u_x
FROM            [es_items]
WHERE        u1_to_u2 > 0
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u3_id] AS u_id, [u3_price] AS u_price, 
                         [u3_cost] AS u_cost, 1.0 / u1_to_u2 / u2_to_u3 AS u_in_u1, 3 AS u_x
FROM            [es_items]
WHERE         u1_to_u2 > 0 AND  u2_to_u3 > 0


GO
/* #0201 21/8/2025 00:59:43 (0) */


DROP VIEW vu_es_stock_stats

GO
/* #0202 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_stock_stats
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.item_qty, A.rsrvd_qty, B.item_type, B.item_group, B.item_subgr, B.item_form, B.item_make, 
                         B.item_agent, B.u_price, B.u_cost, B.u1_id, B.u_in_u1, B.u_x, A.item_qty * B.u_in_u1 AS item_qty_u1
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.vu_es_item_units AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id AND A.item_unit = B.u_id
WHERE        (A.item_qty <> 0)


GO
/* #0203 21/8/2025 00:59:43 (0) */


DROP VIEW vu_es_doc_line_items_net_sales

GO
/* #0204 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_doc_line_items_net_sales
AS
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], [item_qty1], [item_qty2], [item_discount], [sub_total], [sub_total_cc], 
                         [item_qty_u1], [free_qty_u1], [u1_id], [profit_cc], [from_date], [to_date], [line_status], ref_doc_type, ref_doc_no, [item_qty1] AS item_qty1_sold, 0 AS item_qty1_ret, [item_qty_u1] AS item_qty_u1_sold, 0 AS item_qty_u1_ret, 
                         [sub_total_cc] AS sub_total_cc_sold, 0 AS sub_total_cc_ret, ISNULL([profit_cc], 0) AS profit_cc_sold, 0 AS profit_cc_ret,sub_total AS sub_total_sold, 0 AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '201' OR doc_type = '203' OR doc_type='401'
UNION
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], - 1 * [item_qty1] AS item_qty1, - 1 * [item_qty2] AS item_qty2, 
                         [item_discount], - 1 * [sub_total] AS sub_total, - 1 * [sub_total_cc] AS sub_total_cc, - 1 * [item_qty_u1] AS item_qty_u1, - 1 * ISNULL([free_qty_u1], 0) AS free_qty_u1, [u1_id], - 1 * ISNULL([profit_cc], 0) AS profit_cc, [from_date], 
                         [to_date], [line_status], ref_doc_type, ref_doc_no, 0 AS item_qty1_sold, [item_qty1] AS item_qty1_ret, 0 AS item_qty_u1_sold, [item_qty_u1] AS item_qty_u1_ret, 0 AS sub_total_cc_sold, [sub_total_cc] AS sub_total_cc_ret, 
                         0 AS profit_cc_sold, ISNULL([profit_cc], 0) AS profit_cc_ret,0 AS sub_total_sold, [sub_total] AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '202'


GO
/* #0205 21/8/2025 00:59:43 (0) */


DROP VIEW vu_es_stock_trans

GO
/* #0206 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_stock_trans AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, A.tr_time, A.u1_id, A.u1_cost, A.u1_ave_cost, 
                         A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date
FROM            es_stock_trans AS A LEFT OUTER JOIN
                         dbo.es_sales_docs AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no

GO
/* #0207 21/8/2025 00:59:43 (0) */


DROP VIEW vu_es_stock_trans_price

GO
/* #0208 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_stock_trans_price
AS
SELECT        B.unit_price, B.doc_crncy, A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, 
                         A.tr_time, A.u1_id, A.u1_cost,A.u1_ave_cost, A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date, B.doc_year, B.doc_month, B.unit_cost
FROM            dbo.es_stock_trans AS A LEFT OUTER JOIN
                         dbo.vu_es_doc_line_items AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.line_id = B.line_id AND A.batch_no = ISNULL(B.batch_no, N'0')

GO
/* #0209 21/8/2025 00:59:43 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN phone varchar(20) NULL

GO
/* #0210 21/8/2025 00:59:43 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN address nvarchar(100) NULL

GO
/* #0211 21/8/2025 00:59:43 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN to_date varchar(10) NULL

GO
/* #0212 21/8/2025 00:59:43 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN from_date varchar(10) NULL

GO
/* #0213 21/8/2025 00:59:43 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN warranty nvarchar(100) NULL

GO
/* #0214 21/8/2025 00:59:43 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units]

GO
/* #0215 21/8/2025 00:59:43 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units]
AS
select sys_client_id, store_id,item_id,batch_no, [1] as u1_qty,[2] as u2_qty,[3] as u3_qty
from
(
  select sys_client_id, store_id,item_id,batch_no, item_qty, u_x
  from vu_es_stock_stats
) t
pivot
(
  SUM(item_qty) for u_x in ([1],[2],[3])
) pvt


GO
/* #0216 21/8/2025 00:59:43 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units_details]

GO
/* #0217 21/8/2025 00:59:43 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units_details]
AS
SELECT  A.sys_client_id, A.store_id, A.item_id, A.batch_no, A.u1_qty, A.u2_qty, A.u3_qty, B.u1_id, B.u1_price, B.u1_cost, B.u2_id, B.u2_cost, B.u2_price, B.u3_id, 
                         B.u3_cost, B.u3_price, B.item_group, B.item_name
FROM            dbo.vu_es_stock_qty_pvt_units AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0218 21/8/2025 00:59:43 (0) */


UPDATE A SET A.line_id=B.line_id FROM dbo.es_stock_trans A 
INNER JOIN  es_doc_details B
ON A.line_id IS NULL AND A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.batch_no = ISNULL(B.batch_no, N'0')


GO
/* #0219 21/8/2025 00:59:43 (0) */


DROP VIEW vu_es_items_cache

GO
/* #0220 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_items_cache
AS
SELECT        A.item_id, A.item_name, A.sci_name, A.item_code, A.for_name, A.item_status, A.item_type, A.batch_type, A.item_group, A.def_purch_unit, A.def_sale_unit, 
                         A.item_crncy, A.u1_id, A.u1_price, A.u1_cost, A.u1_min_price, A.u2_id, A.u2_price, A.u2_cost, A.u2_min_price, A.u3_id, A.u3_price, A.u3_cost, A.u3_min_price, 
                         A.item_spec, A.item_tax_pct, A.sys_client_id, A.item_flags, B.item_qty_u1, 
                         A.u1_price_s1, A.u1_price_s2, A.u1_price_s3, A.u2_price_s1, A.u2_price_s2, A.u2_price_s3, A.u3_price_s1, A.u3_price_s2, A.u3_price_s3
FROM            dbo.es_items AS A LEFT OUTER JOIN
                             (SELECT sys_client_id, item_id, u1_id, SUM(item_qty_u1) AS item_qty_u1, SUM(item_qty * u_cost) AS u_cost
                                FROM dbo.vu_es_stock_stats
                                GROUP BY sys_client_id, item_id, u1_id) AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id

GO
/* #0221 21/8/2025 00:59:43 (0) */



DROP VIEW vu_es_booking_sched

GO
/* #0222 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_booking_sched
AS
SELECT A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.cov_name, A.address, A.phone, A.from_date, A.to_date, A.net_amount, A.paid_installs, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, 
                         SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.crtd_by, A.crtd_time, B.item_id, B.item_unit, B.batch_no, B.book_status, B.book_date, 
                         B.item_qty, B.is_sub, C.item_group, A.rem_amount, A.paid, A.pnet_amount
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_booking_sched AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0223 21/8/2025 00:59:43 (0) */


DROP VIEW vu_es_cash_docs

GO
/* #0224 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_cash_docs
AS
SELECT sys_client_id, doc_type, doc_no, doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, paid AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND paid > 0
UNION
SELECT sys_client_id, doc_type, doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, pnet_amount AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND pnet_amount > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,1 as cash_dir, pay_type, cash_acc_no AS doc_acc_no, amount AS in_amnt, 0 AS out_amnt
FROM  es_fin_docs WHERE doc_type='101'
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,2 as cash_dir, pay_type, cash_acc_no AS doc_acc_no,0 AS in_amnt, amount AS out_amnt
FROM  es_fin_docs WHERE doc_type='102' 
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, 0 AS in_amnt, paid AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND paid > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, 0 AS in_amnt, pnet_amount AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND pnet_amount > 0


GO
/* #0225 21/8/2025 00:59:43 (0) */


DROP VIEW vu_es_actual_sales_docs

GO
/* #0226 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_actual_sales_docs
AS
SELECT *, net_amount * ISNULL(doc_crncy_exrate, 1) AS net_amount_cc FROM es_sales_docs WHERE (doc_type = '201') OR (doc_type = '203') OR (doc_type = '206') OR  (doc_type = '208') OR (doc_type = '401')

GO
/* #0227 21/8/2025 00:59:43 (0) */


DROP VIEW vu_es_net_sales_docs

GO
/* #0228 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_es_net_sales_docs
AS
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, net_amount,net_amount as sales_amount, 0 as ret_amount
FROM es_sales_docs WHERE  (doc_type = '201') OR  (doc_type = '203') OR (doc_type = '206') OR (doc_type = '208') OR (doc_type = '401')  
UNION
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, -net_amount, 0 as sales_amount, -net_amount as ret_amount
FROM es_sales_docs WHERE  (doc_type = '202')

GO
/* #0229 21/8/2025 00:59:43 (0) */


DROP VIEW fi_vu_trans_entry_sd

GO
/* #0230 21/8/2025 00:59:43 (0) */


CREATE VIEW fi_vu_trans_entry_sd
AS
SELECT B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, B.amount_debit - B.amount_credit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, 
                  B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, B.amount_credit_cc, B.amount_debit_cc - B.amount_credit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, B.proj_id, B.actvty_id, 
                  B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar, 
                  B.tr_entry_no) + '-' + CONVERT(varchar, B.entry_line_no) AS line_uid, C.cust_no, C.cust_grp, C.region, C.sales_rep
FROM     dbo.fi_trans AS A RIGHT OUTER JOIN
                  dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id 
                  INNER JOIN
                  dbo.es_cust AS C ON B.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.cust_no

GO
/* #0231 21/8/2025 00:59:43 (0) */


DROP INDEX fi_trans_doc_data_index ON fi_trans_entry

GO
/* #0232 21/8/2025 00:59:43 (0) */

CREATE NONCLUSTERED INDEX fi_trans_doc_data_index ON fi_trans_entry (doc_type ASC,doc_no ASC) 

GO
/* #0233 21/8/2025 00:59:43 (0) */


DROP INDEX es_sales_docs_date_index ON es_sales_docs

GO
/* #0234 21/8/2025 00:59:43 (0) */


CREATE INDEX es_sales_docs_date_index ON es_sales_docs (doc_date ASC)

GO
/* #0235 21/8/2025 00:59:43 (0) */



DROP VIEW vu_hr_clock

GO
/* #0236 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_hr_clock AS
SELECT A.*, emp_title,branch,emp_dept,emp_section,emp_ou FROM hr_clock A
LEFT JOIN hr_emp_data B
ON
A.sys_client_id=B.sys_client_id AND A.emp_no=B.emp_no

GO
/* #0237 21/8/2025 00:59:43 (0) */


DROP VIEW vu_hr_ts_doc_details

GO
/* #0238 21/8/2025 00:59:43 (0) */


CREATE VIEW vu_hr_ts_doc_details AS
SELECT A.*, doc_status FROM  hr_ts_doc_details A
LEFT JOIN hr_ts_docs B
ON
A.sys_client_id=B.sys_client_id AND A.doc_type=B.doc_type AND A.doc_no = B.doc_no

GO
/* #0239 21/8/2025 01:02:00 (0) */

UPDATE hs_act_log SET act_code='change' WHERE act_code='edit' AND act_desc IS NOT NULL

GO
/* #0240 21/8/2025 01:02:00 (0) */

ALTER TABLE hs_shared_table ALTER COLUMN obj_id varchar(32) NOT NULL

GO
/* #0241 21/8/2025 01:02:00 (0) */

ALTER TABLE hs_attached_docs ALTER COLUMN f nvarchar(500) NULL

GO
/* #0242 21/8/2025 01:02:00 (0) */



ALTER TABLE hs_act_log ALTER COLUMN obj_type varchar(8) NULL

GO
/* #0243 21/8/2025 01:02:00 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_code varchar(8) NULL

GO
/* #0244 21/8/2025 01:02:00 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_at varchar(14) NULL

GO
/* #0245 21/8/2025 01:02:00 (0) */


UPDATE hs_crncy SET max_rate=9999 WHERE sys_client_id is not null and max_rate > 9999

GO
/* #0246 21/8/2025 01:02:00 (0) */


UPDATE fi_accounts SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0247 21/8/2025 01:02:00 (0) */


UPDATE fi_trans_entry SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0248 21/8/2025 01:02:00 (0) */


UPDATE A SET A.doc_type=B.doc_type, A.doc_no=B.doc_no FROM fi_trans_entry A INNER JOIN fi_trans B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id
WHERE A.doc_type IS NULL AND A.doc_no IS NULL

GO
/* #0249 21/8/2025 01:02:00 (0) */


DROP VIEW fi_vu_trans_entry

GO
/* #0250 21/8/2025 01:02:00 (0) */


CREATE VIEW fi_vu_trans_entry
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, B.cov_amount_debit, B.cov_amount_credit, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.cov_crncy, 
                         B.cov_crncy_exrate, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.sub_gl_no, B.line_ref_no, B.line_ref_date,
                         B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid, B.doc_year + B.doc_month AS doc_ym
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id

GO
/* #0251 21/8/2025 01:02:00 (0) */


DROP VIEW fi_vu_trans_entry_ex

GO
/* #0252 21/8/2025 01:02:00 (0) */


CREATE VIEW fi_vu_trans_entry_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, B.sub_gl_no, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.ma_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0253 21/8/2025 01:02:00 (0) */


DROP VIEW fi_vu_trans_entry_cov_ex

GO
/* #0254 21/8/2025 01:02:00 (0) */


CREATE VIEW fi_vu_trans_entry_cov_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0255 21/8/2025 01:02:00 (0) */





DROP VIEW vu_biz_docs

GO
/* #0256 21/8/2025 01:02:00 (0) */


CREATE VIEW vu_biz_docs
AS
SELECT        sys_client_id, doc_type, doc_no, cash_id AS doc_acc_no, cov_no, price AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         price * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_sales_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, cash_acc_no AS doc_acc_no, cov_no, amount AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         amount * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_fin_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, NULL AS doc_acc_no, NULL AS cov_no, amount AS doc_amount, doc_crncy, entry_memo AS doc_note, doc_status, 
                         suspended, draft, doc_date, ref_doc_type, ref_doc_no, crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, 
                        1 AS doc_crncy_exrate, amount  AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) 
                         AS doc_month
FROM            fi_gl_entries


GO
/* #0257 21/8/2025 01:02:00 (0) */


DROP VIEW vu_es_doc_line_items

GO
/* #0258 21/8/2025 01:02:00 (0) */


CREATE VIEW vu_es_doc_line_items
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) 
                         AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no, C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, 
                         C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, 
                         CAST(ISNULL(A.doc_crncy_exrate, 1) * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, B.ex_data30, B.line_id, ISNULL(B.branch, 
                         A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id, C.branch AS item_branch, C.proj_id AS item_proj
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0259 21/8/2025 01:02:00 (0) */


DROP VIEW vu_es_doc_line_items_ex

GO
/* #0260 21/8/2025 01:02:00 (0) */


CREATE VIEW vu_es_doc_line_items_ex
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no,A.cov_name, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) 
                         AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no,
                        C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 
                         1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, CAST(ISNULL(A.doc_crncy_exrate, 1) 
                         * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, 
                         B.ex_data30, B.line_id, ISNULL(B.branch, A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0261 21/8/2025 01:02:00 (0) */



DROP VIEW vu_es_stock_qty

GO
/* #0262 21/8/2025 01:02:00 (0) */


CREATE VIEW vu_es_stock_qty
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, B.item_name, A.item_unit, A.batch_no, A.unit_cost, A.item_qty, A.rsrvd_qty, B.item_group, B.for_name, B.sci_name, 
                         B.item_code, B.item_agent, B.item_make
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0263 21/8/2025 01:02:00 (0) */


DROP VIEW vu_es_item_units

GO
/* #0264 21/8/2025 01:02:00 (0) */


CREATE VIEW vu_es_item_units
AS
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u1_id] AS u_id, [u1_price] AS u_price, 
                         [u1_cost] AS u_cost, 1.0 AS u_in_u1, 1 AS u_x
FROM            [es_items]
WHERE        u1_id IS NOT NULL
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u2_id] AS u_id, [u2_price] AS u_price, 
                         [u2_cost] AS u_cost, 1.0 / u1_to_u2 AS u_in_u1, 2 AS u_x
FROM            [es_items]
WHERE        u1_to_u2 > 0
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u3_id] AS u_id, [u3_price] AS u_price, 
                         [u3_cost] AS u_cost, 1.0 / u1_to_u2 / u2_to_u3 AS u_in_u1, 3 AS u_x
FROM            [es_items]
WHERE         u1_to_u2 > 0 AND  u2_to_u3 > 0


GO
/* #0265 21/8/2025 01:02:00 (0) */


DROP VIEW vu_es_stock_stats

GO
/* #0266 21/8/2025 01:02:00 (0) */


CREATE VIEW vu_es_stock_stats
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.item_qty, A.rsrvd_qty, B.item_type, B.item_group, B.item_subgr, B.item_form, B.item_make, 
                         B.item_agent, B.u_price, B.u_cost, B.u1_id, B.u_in_u1, B.u_x, A.item_qty * B.u_in_u1 AS item_qty_u1
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.vu_es_item_units AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id AND A.item_unit = B.u_id
WHERE        (A.item_qty <> 0)


GO
/* #0267 21/8/2025 01:02:00 (0) */


DROP VIEW vu_es_doc_line_items_net_sales

GO
/* #0268 21/8/2025 01:02:00 (0) */


CREATE VIEW vu_es_doc_line_items_net_sales
AS
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], [item_qty1], [item_qty2], [item_discount], [sub_total], [sub_total_cc], 
                         [item_qty_u1], [free_qty_u1], [u1_id], [profit_cc], [from_date], [to_date], [line_status], ref_doc_type, ref_doc_no, [item_qty1] AS item_qty1_sold, 0 AS item_qty1_ret, [item_qty_u1] AS item_qty_u1_sold, 0 AS item_qty_u1_ret, 
                         [sub_total_cc] AS sub_total_cc_sold, 0 AS sub_total_cc_ret, ISNULL([profit_cc], 0) AS profit_cc_sold, 0 AS profit_cc_ret,sub_total AS sub_total_sold, 0 AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '201' OR doc_type = '203' OR doc_type='401'
UNION
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], - 1 * [item_qty1] AS item_qty1, - 1 * [item_qty2] AS item_qty2, 
                         [item_discount], - 1 * [sub_total] AS sub_total, - 1 * [sub_total_cc] AS sub_total_cc, - 1 * [item_qty_u1] AS item_qty_u1, - 1 * ISNULL([free_qty_u1], 0) AS free_qty_u1, [u1_id], - 1 * ISNULL([profit_cc], 0) AS profit_cc, [from_date], 
                         [to_date], [line_status], ref_doc_type, ref_doc_no, 0 AS item_qty1_sold, [item_qty1] AS item_qty1_ret, 0 AS item_qty_u1_sold, [item_qty_u1] AS item_qty_u1_ret, 0 AS sub_total_cc_sold, [sub_total_cc] AS sub_total_cc_ret, 
                         0 AS profit_cc_sold, ISNULL([profit_cc], 0) AS profit_cc_ret,0 AS sub_total_sold, [sub_total] AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '202'


GO
/* #0269 21/8/2025 01:02:00 (0) */


DROP VIEW vu_es_stock_trans

GO
/* #0270 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_es_stock_trans AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, A.tr_time, A.u1_id, A.u1_cost, A.u1_ave_cost, 
                         A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date
FROM            es_stock_trans AS A LEFT OUTER JOIN
                         dbo.es_sales_docs AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no

GO
/* #0271 21/8/2025 01:02:01 (0) */


DROP VIEW vu_es_stock_trans_price

GO
/* #0272 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_es_stock_trans_price
AS
SELECT        B.unit_price, B.doc_crncy, A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, 
                         A.tr_time, A.u1_id, A.u1_cost,A.u1_ave_cost, A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date, B.doc_year, B.doc_month, B.unit_cost
FROM            dbo.es_stock_trans AS A LEFT OUTER JOIN
                         dbo.vu_es_doc_line_items AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.line_id = B.line_id AND A.batch_no = ISNULL(B.batch_no, N'0')

GO
/* #0273 21/8/2025 01:02:01 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN phone varchar(20) NULL

GO
/* #0274 21/8/2025 01:02:01 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN address nvarchar(100) NULL

GO
/* #0275 21/8/2025 01:02:01 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN to_date varchar(10) NULL

GO
/* #0276 21/8/2025 01:02:01 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN from_date varchar(10) NULL

GO
/* #0277 21/8/2025 01:02:01 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN warranty nvarchar(100) NULL

GO
/* #0278 21/8/2025 01:02:01 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units]

GO
/* #0279 21/8/2025 01:02:01 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units]
AS
select sys_client_id, store_id,item_id,batch_no, [1] as u1_qty,[2] as u2_qty,[3] as u3_qty
from
(
  select sys_client_id, store_id,item_id,batch_no, item_qty, u_x
  from vu_es_stock_stats
) t
pivot
(
  SUM(item_qty) for u_x in ([1],[2],[3])
) pvt


GO
/* #0280 21/8/2025 01:02:01 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units_details]

GO
/* #0281 21/8/2025 01:02:01 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units_details]
AS
SELECT  A.sys_client_id, A.store_id, A.item_id, A.batch_no, A.u1_qty, A.u2_qty, A.u3_qty, B.u1_id, B.u1_price, B.u1_cost, B.u2_id, B.u2_cost, B.u2_price, B.u3_id, 
                         B.u3_cost, B.u3_price, B.item_group, B.item_name
FROM            dbo.vu_es_stock_qty_pvt_units AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0282 21/8/2025 01:02:01 (0) */


UPDATE A SET A.line_id=B.line_id FROM dbo.es_stock_trans A 
INNER JOIN  es_doc_details B
ON A.line_id IS NULL AND A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.batch_no = ISNULL(B.batch_no, N'0')


GO
/* #0283 21/8/2025 01:02:01 (0) */


DROP VIEW vu_es_items_cache

GO
/* #0284 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_es_items_cache
AS
SELECT        A.item_id, A.item_name, A.sci_name, A.item_code, A.for_name, A.item_status, A.item_type, A.batch_type, A.item_group, A.def_purch_unit, A.def_sale_unit, 
                         A.item_crncy, A.u1_id, A.u1_price, A.u1_cost, A.u1_min_price, A.u2_id, A.u2_price, A.u2_cost, A.u2_min_price, A.u3_id, A.u3_price, A.u3_cost, A.u3_min_price, 
                         A.item_spec, A.item_tax_pct, A.sys_client_id, A.item_flags, B.item_qty_u1, 
                         A.u1_price_s1, A.u1_price_s2, A.u1_price_s3, A.u2_price_s1, A.u2_price_s2, A.u2_price_s3, A.u3_price_s1, A.u3_price_s2, A.u3_price_s3
FROM            dbo.es_items AS A LEFT OUTER JOIN
                             (SELECT sys_client_id, item_id, u1_id, SUM(item_qty_u1) AS item_qty_u1, SUM(item_qty * u_cost) AS u_cost
                                FROM dbo.vu_es_stock_stats
                                GROUP BY sys_client_id, item_id, u1_id) AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id

GO
/* #0285 21/8/2025 01:02:01 (0) */



DROP VIEW vu_es_booking_sched

GO
/* #0286 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_es_booking_sched
AS
SELECT A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.cov_name, A.address, A.phone, A.from_date, A.to_date, A.net_amount, A.paid_installs, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, 
                         SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.crtd_by, A.crtd_time, B.item_id, B.item_unit, B.batch_no, B.book_status, B.book_date, 
                         B.item_qty, B.is_sub, C.item_group, A.rem_amount, A.paid, A.pnet_amount
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_booking_sched AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0287 21/8/2025 01:02:01 (0) */


DROP VIEW vu_es_cash_docs

GO
/* #0288 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_es_cash_docs
AS
SELECT sys_client_id, doc_type, doc_no, doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, paid AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND paid > 0
UNION
SELECT sys_client_id, doc_type, doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, pnet_amount AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND pnet_amount > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,1 as cash_dir, pay_type, cash_acc_no AS doc_acc_no, amount AS in_amnt, 0 AS out_amnt
FROM  es_fin_docs WHERE doc_type='101'
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,2 as cash_dir, pay_type, cash_acc_no AS doc_acc_no,0 AS in_amnt, amount AS out_amnt
FROM  es_fin_docs WHERE doc_type='102' 
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, 0 AS in_amnt, paid AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND paid > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, 0 AS in_amnt, pnet_amount AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND pnet_amount > 0


GO
/* #0289 21/8/2025 01:02:01 (0) */


DROP VIEW vu_es_actual_sales_docs

GO
/* #0290 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_es_actual_sales_docs
AS
SELECT *, net_amount * ISNULL(doc_crncy_exrate, 1) AS net_amount_cc FROM es_sales_docs WHERE (doc_type = '201') OR (doc_type = '203') OR (doc_type = '206') OR  (doc_type = '208') OR (doc_type = '401')

GO
/* #0291 21/8/2025 01:02:01 (0) */


DROP VIEW vu_es_net_sales_docs

GO
/* #0292 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_es_net_sales_docs
AS
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, net_amount,net_amount as sales_amount, 0 as ret_amount
FROM es_sales_docs WHERE  (doc_type = '201') OR  (doc_type = '203') OR (doc_type = '206') OR (doc_type = '208') OR (doc_type = '401')  
UNION
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, -net_amount, 0 as sales_amount, -net_amount as ret_amount
FROM es_sales_docs WHERE  (doc_type = '202')

GO
/* #0293 21/8/2025 01:02:01 (0) */


DROP VIEW fi_vu_trans_entry_sd

GO
/* #0294 21/8/2025 01:02:01 (0) */


CREATE VIEW fi_vu_trans_entry_sd
AS
SELECT B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, B.amount_debit - B.amount_credit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, 
                  B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, B.amount_credit_cc, B.amount_debit_cc - B.amount_credit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, B.proj_id, B.actvty_id, 
                  B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar, 
                  B.tr_entry_no) + '-' + CONVERT(varchar, B.entry_line_no) AS line_uid, C.cust_no, C.cust_grp, C.region, C.sales_rep
FROM     dbo.fi_trans AS A RIGHT OUTER JOIN
                  dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id 
                  INNER JOIN
                  dbo.es_cust AS C ON B.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.cust_no

GO
/* #0295 21/8/2025 01:02:01 (0) */


DROP INDEX fi_trans_doc_data_index ON fi_trans_entry

GO
/* #0296 21/8/2025 01:02:01 (0) */

CREATE NONCLUSTERED INDEX fi_trans_doc_data_index ON fi_trans_entry (doc_type ASC,doc_no ASC) 

GO
/* #0297 21/8/2025 01:02:01 (0) */


DROP INDEX es_sales_docs_date_index ON es_sales_docs

GO
/* #0298 21/8/2025 01:02:01 (0) */


CREATE INDEX es_sales_docs_date_index ON es_sales_docs (doc_date ASC)

GO
/* #0299 21/8/2025 01:02:01 (0) */



DROP VIEW vu_hr_clock

GO
/* #0300 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_hr_clock AS
SELECT A.*, emp_title,branch,emp_dept,emp_section,emp_ou FROM hr_clock A
LEFT JOIN hr_emp_data B
ON
A.sys_client_id=B.sys_client_id AND A.emp_no=B.emp_no

GO
/* #0301 21/8/2025 01:02:01 (0) */


DROP VIEW vu_hr_ts_doc_details

GO
/* #0302 21/8/2025 01:02:01 (0) */


CREATE VIEW vu_hr_ts_doc_details AS
SELECT A.*, doc_status FROM  hr_ts_doc_details A
LEFT JOIN hr_ts_docs B
ON
A.sys_client_id=B.sys_client_id AND A.doc_type=B.doc_type AND A.doc_no = B.doc_no

GO
/* #0303 21/8/2025 01:05:26 (1) */
UPDATE hs_config SET cfg_value='Y' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0001 21/8/2025 01:05:26 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0002 21/8/2025 01:05:26 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0003 21/8/2025 01:05:26 (1) */
UPDATE hs_config SET cfg_value='lrisv6aVu7O84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0004 21/8/2025 01:05:26 (1) */
UPDATE hs_config SET cfg_value='kKKv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0005 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0006 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0007 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0008 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0009 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0010 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0011 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0012 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0013 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0014 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0015 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0016 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0017 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0018 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0019 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0020 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0021 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0022 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0023 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0024 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0025 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0026 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0027 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0028 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0029 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0030 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0031 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0032 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0033 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0034 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0035 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0036 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0037 21/8/2025 01:05:26 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0038 21/8/2025 01:05:26 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0039 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0040 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0041 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0042 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0043 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0044 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0045 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0046 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0047 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0048 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0049 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0050 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0051 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0052 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0053 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0054 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0055 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0056 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0057 21/8/2025 01:05:27 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0058 21/8/2025 01:05:27 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0059 21/8/2025 01:05:42 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRHD2MWS91',NULL,'SYS','admin','Successfull login','********','010542','W',0,0,NULL,'sys',NULL)
GO
/* #0060 21/8/2025 01:05:42 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:58140 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********010542','9900')
GO
/* #0061 21/8/2025 01:05:42 (1) */
UPDATE hs_logindata SET last_activity_dt='********010542' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0062 21/8/2025 01:07:40 (1) */
DELETE FROM hs_objects WHERE sys_client_id='9900' AND (obj_type='MENU' AND owner_id='admin' AND obj_id='USERFAV')
GO
/* #0063 21/8/2025 01:07:40 (1) */
INSERT INTO hs_objects (obj_type,owner_id,obj_id,obj_flags,obj_media,obj_data,chgd_by,chgd_time,sys_client_id) VALUES ('MENU','admin','USERFAV',0,'X','<?xml version="1.0" encoding="utf-16"?>
<HsMenu xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <title>المفضلة</title>
  <css_class>icon_menu</css_class>
  <show_drop_down_sign>false</show_drop_down_sign>
  <add_to_sys_user_default_menu>true</add_to_sys_user_default_menu>
  <is_app_menu>false</is_app_menu>
  <items>
    <HsMenuItem>
      <type>Link</type>
      <text>الموظفين</text>
      <url>hr_menu.html</url>
      <open_in_dlg>false</open_in_dlg>
    </HsMenuItem>
    <HsMenuItem>
      <type>Link</type>
      <text>شؤون الموظفين</text>
      <url>hr_shortcut.html</url>
      <open_in_dlg>false</open_in_dlg>
    </HsMenuItem>
  </items>
</HsMenu>','admin','********010740','9900')
GO
/* #0064 21/8/2025 01:09:49 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24JRII0EPHB',NULL,'SYS','admin','Successfull login','********','010949','W',0,0,NULL,'sys',NULL)
GO
/* #0065 21/8/2025 01:09:49 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:58475 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','********010949','9900')
GO
/* #0066 21/8/2025 01:09:49 (1) */
UPDATE hs_logindata SET last_activity_dt='********010949' WHERE sys_client_id='9900' AND user_id='admin'
GO

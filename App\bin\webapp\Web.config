<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.web>

    <pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID" />
    <compilation targetFramework="4.8">

    </compilation>
    <customErrors mode="RemoteOnly" defaultRedirect="/app/s/htmls/sys-error.html" />
    <sessionState mode="InProc" timeout="60" cookieName="HsApps-SID" regenerateExpiredSessionId="false" />
    <httpRuntime requestValidationMode="2.0" maxRequestLength="51200" executionTimeout="1110" />
    <globalization culture="ar-YE" />

  </system.web>

  <appSettings>
<add key="hs.sys_edition_type" value="2" />
<add key="db.enable_reset" value="no" />
<add key="db.enable_open_sqls_batch" value="yes" />
<add key="hs.enable_user_fields" value="no" />
<add key="hs.amount-decimals" value="2" />
<add key="hs.enable_client_files" value="yes" />
<add key="es.enable_smart_store" value="yes" />
<add key="es.enable_open_store" value="yes" />
<add key="hs.use-modern-page-handler" value="yes"/>
<add key="hs.enable-app-link" value="yes"/>
<add key="db.enable_sql_arch" value="no"/>
<add key="hs.max_failed_logins" value="10"/>
<add key="hs.max_suspicious_actions" value="5"/>
<add key="hs.enable_hs_rating" value="yes"/>
<add key="es.enable_remox_hcm" value="yes"/>
<add key="hr.enable_menu" value="yes"/>
<add key="hr.show_in_main_menu" value="yes"/>
<add key="hs.admin_full_access" value="yes"/>
<add key="hs.enable_all_modules" value="yes"/>
<add key="hr.admin_access" value="yes"/>
<add key="hcm.menu_enabled" value="yes"/>
<add key="hcm.menu_id" value="hcm-sys"/>
<add key="es.enable_gen_service_item" value="yes"/>
<add key="es.enable_online_link" value="yes"/>
<add key="es.item_img_max_size" value="200"/>
<add key="hs.enable_hs_rating" value="yes"/>

    <add key="hs.app_lang_scode" value="ar" />
    <add key="hs.app_short_name" value="HsApp" />
    <add key="hs.app_title" value="RemoX" />
    <add key="hs.app_client_name" value="متجر" />
    <!-- override default app_data_path -->
    <add key="hs.app_data_path" value="D:\\mohhash\\Data\\" />
    <add key="hs.app_backup_path" value="D:\\mohhash\\Backup\\" />
    <add key="hs.app_virtual_root" value="" />
    <add key="hs.app_date_fmt" value="d/M/yyyy" />
    <add key="hs.app_time_fmt" value="HH:mm:ss" />
    <add key="hs.app_datetime_fmt" value="dd/MM/yyyy HH:mm:ss" />
    <add key="hs.enable_dev_mode" value="no" />
    <!-- 2:fatal,3:err,4:warn,(5:info),7:trace -->
    <add key="hs.sys_log_level" value="3" />
    <add key="hs.enable_os_cmd_line" value="no" />
    <add key="db.enable_db_trace" value="no" />
    <add key="hs.enable_debug_info" value="no" />
    <add key="hs.enable_offline_mode" value="no" />
    <add key="hs.enable_client_cache_using_ajax" value="yes" />

    <add key="hs.enable_hs_notes" value="yes" />
    <!-- ******  Database: 1=access;2=mssql;3=oracle;4=mysql ****** -->
    <add key="db.dbms_type" value="2" />
    <add key="db.enable_open_sqls" value="yes" />
    <add key="db.enable_delayed_sqls" value="yes" />
    <add key="db.enable_replication" value="no" />
    <!-- show sqls -->
    <add key="db.enable_db_trace" value="no" />
    <add key="db.enable_db_mon" value="no" />
    <!-- ******  Database connection string *********-->

  <add key="hs.app_shared_db_conn_str" value="Provider=SQLOLEDB.1;User ID=sa;Password=[#db.password];Persist Security Info=True;Initial Catalog=HsAppsDb_Shared_mohhash;Data Source=.\SQLREMOX;Connection Timeout=600" />
  <add key="hs.app_db_password" value="6fyqnqKm2MLv9NjS" />



    <!-- ******** Multi Client System -->

    <add key="hs.shared_system" value="no" />
    <add key="hs.enterprise_system" value="no" />
    <add key="hs.def_sys_client_id" value="9900" />
    <add key="hs.enable_public_clt_reg" value="yes" />

   <!-- ********* RemoX Sales ********-->

    <add key="es.enable_allow_negative_stock" value="yes" />



  </appSettings>
  <system.webServer>
    <handlers>
      <add verb="*" path="/store/*.*" name="MyHttpHandler" type="MyHttpHandler" />
      <add verb="*" path="/tmp/*.*" name="MyHttpHandler2" type="MyHttpHandler" />

      <add verb="*" path="*.json" name="HsJsonHandlerName" type="HsJsonHandler" />

    </handlers>



  </system.webServer>


</configuration>

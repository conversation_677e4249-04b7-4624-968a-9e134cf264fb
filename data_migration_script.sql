-- سكريپت نقل البيانات من قاعدة بيانات mohaminew إلى mohhash
-- تاريخ الإنشاء: 2025-08-21
-- الهدف: نقل جميع البيانات الموجودة مع الحفاظ على العلاقات

-- =====================================================
-- تحضير البيانات للنقل
-- =====================================================

-- إنشاء اتصال مع قاعدة البيانات المصدر
-- \c mohaminew

-- =====================================================
-- نقل البيانات الأساسية
-- =====================================================

-- 1. نقل المحافظات
INSERT INTO mohhash.governorates (id, name, code, is_active, created_date)
SELECT id, name, code, is_active, created_date
FROM mohaminew.governorates
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    code = EXCLUDED.code,
    is_active = EXCLUDED.is_active;

-- 2. نقل المحاكم
INSERT INTO mohhash.courts (id, name, governorate_id, address, phone, email, is_active, created_date)
SELECT id, name, governorate_id, address, phone, email, is_active, created_date
FROM mohaminew.courts
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    governorate_id = EXCLUDED.governorate_id,
    address = EXCLUDED.address,
    phone = EXCLUDED.phone,
    email = EXCLUDED.email,
    is_active = EXCLUDED.is_active;

-- 3. نقل الفروع
INSERT INTO mohhash.branches (id, name, governorate_id, address, phone, manager_name, is_active, created_date)
SELECT id, name, governorate_id, address, phone, manager_name, is_active, created_date
FROM mohaminew.branches
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    governorate_id = EXCLUDED.governorate_id,
    address = EXCLUDED.address,
    phone = EXCLUDED.phone,
    manager_name = EXCLUDED.manager_name,
    is_active = EXCLUDED.is_active;

-- 4. نقل أنواع القضايا
INSERT INTO mohhash.issue_types (id, name, description, is_active, created_date)
SELECT id, name, description, is_active, created_date
FROM mohaminew.issue_types
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    is_active = EXCLUDED.is_active;

-- 5. نقل الخدمات
INSERT INTO mohhash.services (id, name, description, default_percentage, is_active, created_date)
SELECT id, name, description, 
       COALESCE(default_percentage, 0) as default_percentage,
       is_active, created_date
FROM mohaminew.services
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    default_percentage = EXCLUDED.default_percentage,
    is_active = EXCLUDED.is_active;

-- 6. نقل النسب المالية
INSERT INTO mohhash.lineages (id, name, admin_percentage, created_date)
SELECT id, name, admin_percentage, created_date
FROM mohaminew.lineages
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    admin_percentage = EXCLUDED.admin_percentage;

-- =====================================================
-- نقل بيانات العملاء والموظفين
-- =====================================================

-- 7. نقل العملاء (دمج مع الجدول الموجود)
INSERT INTO mohhash.clients (
    id, name, phone, email, address, governorate_id, 
    id_number, client_type, is_active, created_date, account_id
)
SELECT 
    id, name, phone, email, address, governorate_id,
    id_number, client_type, is_active, created_date, account_id
FROM mohaminew.clients
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    phone = EXCLUDED.phone,
    email = EXCLUDED.email,
    address = EXCLUDED.address,
    governorate_id = EXCLUDED.governorate_id,
    id_number = EXCLUDED.id_number,
    client_type = EXCLUDED.client_type,
    is_active = EXCLUDED.is_active,
    account_id = EXCLUDED.account_id;

-- 8. نقل الموظفين (دمج مع الجدول الموجود)
INSERT INTO mohhash.employees (
    id, name, position, department, phone, email, 
    governorate_id, branch_id, is_active, created_date, account_id
)
SELECT 
    id, name, position, department, phone, email,
    governorate_id, branch_id, is_active, created_date, account_id
FROM mohaminew.employees
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    position = EXCLUDED.position,
    department = EXCLUDED.department,
    phone = EXCLUDED.phone,
    email = EXCLUDED.email,
    governorate_id = EXCLUDED.governorate_id,
    branch_id = EXCLUDED.branch_id,
    is_active = EXCLUDED.is_active,
    account_id = EXCLUDED.account_id;

-- =====================================================
-- نقل بيانات القضايا والمتابعة
-- =====================================================

-- 9. نقل القضايا
INSERT INTO mohhash.issues (
    id, case_number, title, description, client_id, client_name,
    court_id, court_name, issue_type_id, issue_type, status,
    amount, contract_method, contract_date, next_hearing, notes,
    account_id, created_date, updated_at
)
SELECT 
    id, case_number, title, description, client_id, client_name,
    court_id, court_name, issue_type_id, issue_type, status,
    COALESCE(amount, 0) as amount, 
    COALESCE(contract_method, 'بالجلسة') as contract_method,
    contract_date, next_hearing, notes, account_id, created_date, updated_at
FROM mohaminew.issues
ON CONFLICT (id) DO UPDATE SET
    case_number = EXCLUDED.case_number,
    title = EXCLUDED.title,
    description = EXCLUDED.description,
    client_id = EXCLUDED.client_id,
    client_name = EXCLUDED.client_name,
    court_id = EXCLUDED.court_id,
    court_name = EXCLUDED.court_name,
    issue_type_id = EXCLUDED.issue_type_id,
    issue_type = EXCLUDED.issue_type,
    status = EXCLUDED.status,
    amount = EXCLUDED.amount,
    contract_method = EXCLUDED.contract_method,
    contract_date = EXCLUDED.contract_date,
    next_hearing = EXCLUDED.next_hearing,
    notes = EXCLUDED.notes,
    account_id = EXCLUDED.account_id,
    updated_at = EXCLUDED.updated_at;

-- 10. نقل توزيع القضايا
INSERT INTO mohhash.case_distribution (
    id, issue_id, lineage_id, distribution_date, notes, created_date
)
SELECT 
    id, issue_id, lineage_id, distribution_date, notes, created_date
FROM mohaminew.case_distribution
ON CONFLICT (id) DO UPDATE SET
    issue_id = EXCLUDED.issue_id,
    lineage_id = EXCLUDED.lineage_id,
    distribution_date = EXCLUDED.distribution_date,
    notes = EXCLUDED.notes;

-- 11. نقل توزيع الخدمات
INSERT INTO mohhash.service_distributions (
    id, case_distribution_id, service_id, lawyer_id, 
    percentage, amount, status, created_date
)
SELECT 
    id, case_distribution_id, service_id, lawyer_id,
    COALESCE(percentage, 0) as percentage,
    COALESCE(amount, 0) as amount,
    COALESCE(status, 'active') as status,
    created_date
FROM mohaminew.service_distributions
ON CONFLICT (id) DO UPDATE SET
    case_distribution_id = EXCLUDED.case_distribution_id,
    service_id = EXCLUDED.service_id,
    lawyer_id = EXCLUDED.lawyer_id,
    percentage = EXCLUDED.percentage,
    amount = EXCLUDED.amount,
    status = EXCLUDED.status;

-- 12. نقل المتابعات
INSERT INTO mohhash.follows (
    id, case_id, service_id, user_id, report, date_field,
    status, next_hearing_id, notes, created_date
)
SELECT 
    id, case_id, service_id, user_id, report, 
    COALESCE(date_field, CURRENT_DATE) as date_field,
    COALESCE(status, 'pending') as status,
    next_hearing_id, notes, created_date
FROM mohaminew.follows
ON CONFLICT (id) DO UPDATE SET
    case_id = EXCLUDED.case_id,
    service_id = EXCLUDED.service_id,
    user_id = EXCLUDED.user_id,
    report = EXCLUDED.report,
    date_field = EXCLUDED.date_field,
    status = EXCLUDED.status,
    next_hearing_id = EXCLUDED.next_hearing_id,
    notes = EXCLUDED.notes;

-- =====================================================
-- نقل بيانات النظام والإعدادات
-- =====================================================

-- 13. نقل الإعلانات
INSERT INTO mohhash.announcements (
    id, title, content, type, priority, is_active,
    start_date, end_date, target_users, created_by, created_date
)
SELECT 
    id, title, content, 
    COALESCE(type, 'info') as type,
    COALESCE(priority, 'normal') as priority,
    COALESCE(is_active, true) as is_active,
    start_date, end_date, target_users, created_by, created_date
FROM mohaminew.announcements
ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    content = EXCLUDED.content,
    type = EXCLUDED.type,
    priority = EXCLUDED.priority,
    is_active = EXCLUDED.is_active,
    start_date = EXCLUDED.start_date,
    end_date = EXCLUDED.end_date,
    target_users = EXCLUDED.target_users,
    created_by = EXCLUDED.created_by;

-- 14. نقل إعدادات الذكاء الاصطناعي
INSERT INTO mohhash.ai_settings (
    id, setting_key, setting_value, description, is_active, created_date
)
SELECT 
    id, setting_key, setting_value, description,
    COALESCE(is_active, true) as is_active, created_date
FROM mohaminew.ai_settings
ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    description = EXCLUDED.description,
    is_active = EXCLUDED.is_active;

-- 15. نقل رسائل المحادثة
INSERT INTO mohhash.chat_messages (
    id, user_id, message, message_type, session_id, response_time, created_date
)
SELECT 
    id, user_id, message,
    COALESCE(message_type, 'user') as message_type,
    session_id, response_time, created_date
FROM mohaminew.chat_messages
ON CONFLICT (id) DO UPDATE SET
    user_id = EXCLUDED.user_id,
    message = EXCLUDED.message,
    message_type = EXCLUDED.message_type,
    session_id = EXCLUDED.session_id,
    response_time = EXCLUDED.response_time;

-- =====================================================
-- تحديث المتسلسلات (Sequences)
-- =====================================================

-- تحديث متسلسلات الجداول لتجنب تضارب المفاتيح
SELECT setval('mohhash.governorates_id_seq', (SELECT MAX(id) FROM mohhash.governorates));
SELECT setval('mohhash.courts_id_seq', (SELECT MAX(id) FROM mohhash.courts));
SELECT setval('mohhash.branches_id_seq', (SELECT MAX(id) FROM mohhash.branches));
SELECT setval('mohhash.issue_types_id_seq', (SELECT MAX(id) FROM mohhash.issue_types));
SELECT setval('mohhash.services_id_seq', (SELECT MAX(id) FROM mohhash.services));
SELECT setval('mohhash.lineages_id_seq', (SELECT MAX(id) FROM mohhash.lineages));
SELECT setval('mohhash.issues_id_seq', (SELECT MAX(id) FROM mohhash.issues));
SELECT setval('mohhash.case_distribution_id_seq', (SELECT MAX(id) FROM mohhash.case_distribution));
SELECT setval('mohhash.service_distributions_id_seq', (SELECT MAX(id) FROM mohhash.service_distributions));
SELECT setval('mohhash.follows_id_seq', (SELECT MAX(id) FROM mohhash.follows));
SELECT setval('mohhash.announcements_id_seq', (SELECT MAX(id) FROM mohhash.announcements));
SELECT setval('mohhash.ai_settings_id_seq', (SELECT MAX(id) FROM mohhash.ai_settings));
SELECT setval('mohhash.chat_messages_id_seq', (SELECT MAX(id) FROM mohhash.chat_messages));

-- =====================================================
-- التحقق من نجاح النقل
-- =====================================================

-- عرض إحصائيات النقل
SELECT 'governorates' as table_name, COUNT(*) as records_count FROM mohhash.governorates
UNION ALL
SELECT 'courts', COUNT(*) FROM mohhash.courts
UNION ALL
SELECT 'branches', COUNT(*) FROM mohhash.branches
UNION ALL
SELECT 'issue_types', COUNT(*) FROM mohhash.issue_types
UNION ALL
SELECT 'services', COUNT(*) FROM mohhash.services
UNION ALL
SELECT 'lineages', COUNT(*) FROM mohhash.lineages
UNION ALL
SELECT 'issues', COUNT(*) FROM mohhash.issues
UNION ALL
SELECT 'case_distribution', COUNT(*) FROM mohhash.case_distribution
UNION ALL
SELECT 'service_distributions', COUNT(*) FROM mohhash.service_distributions
UNION ALL
SELECT 'follows', COUNT(*) FROM mohhash.follows
UNION ALL
SELECT 'announcements', COUNT(*) FROM mohhash.announcements
UNION ALL
SELECT 'ai_settings', COUNT(*) FROM mohhash.ai_settings
UNION ALL
SELECT 'chat_messages', COUNT(*) FROM mohhash.chat_messages;

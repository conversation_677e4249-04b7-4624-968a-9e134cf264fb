22/8/2025 04:42:10 ;  ; 37 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


22/8/2025 04:42:10 ;  ; 37 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)


22/8/2025 04:42:10 ;  ; 37 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteScalar()
   at HS.hdb.GetRecordsCount(String table, String cond)


22/8/2025 04:42:16 ;  ; 43 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


22/8/2025 04:42:16 ;  ; 43 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



22/8/2025 04:46:00 ;  ; 13 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


22/8/2025 04:46:00 ;  ; 13 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



22/8/2025 04:48:05 ;  ; 55 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)


22/8/2025 04:48:05 ;  ; 55 ; ::1 ; 9900:admin ; System.Data.OleDb.OleDbException (0x80040E37): Invalid object name 'hr_shift_sched'.
   at System.Data.OleDb.OleDbDataReader.ProcessResults(OleDbHResult hr)
   at System.Data.OleDb.OleDbDataReader.NextResult()
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteReader(CommandBehavior behavior)
   at HS.hdb.RunSql_GetReader(DbConnection db_con, String sql)
   at HS.hdb.Tools.GetDbTableSchema(String table_name)



22/8/2025 04:51:21 ;  ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


22/8/2025 04:51:21 ;  ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


22/8/2025 04:51:21 ;  ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


22/8/2025 04:51:21 ;  ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


22/8/2025 04:51:21 ;  ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)


22/8/2025 04:51:21 ;  ; 47 ; ::1 ; 9900:admin ; System.IndexOutOfRangeException: parent_fld
   at System.Data.ProviderBase.FieldNameLookup.GetOrdinal(String fieldName)
   at HS.hdb.ReadStr(DbDataReader r, String fld_name)




<!DOCTYPE html>
<html>
<head>
    <title>عرض تكامل الأنظمة - النظام المحاسبي</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }
        
        .integration-flow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .flow-step {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            position: relative;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        
        .flow-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .flow-step::after {
            content: '→';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2em;
            color: #667eea;
            font-weight: bold;
        }
        
        .flow-step:last-child::after {
            display: none;
        }
        
        .step-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }
        
        .step-title {
            color: #2c3e50;
            font-size: 1.3em;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .step-description {
            color: #7f8c8d;
            line-height: 1.5;
        }
        
        .integration-examples {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .example-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .example-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .example-description {
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        
        .example-links {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .example-link {
            padding: 8px 15px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 20px;
            font-size: 0.9em;
            transition: background 0.3s;
        }
        
        .example-link:hover {
            background: #5a6fd8;
            text-decoration: none;
            color: white;
        }
        
        .benefits-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .benefit-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }
        
        .benefit-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }
        
        .benefit-title {
            font-size: 1.2em;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .benefit-description {
            font-size: 0.95em;
            line-height: 1.4;
        }
        
        .quick-access {
            background: #e9ecef;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .quick-link {
            display: block;
            padding: 15px;
            background: white;
            border-radius: 10px;
            text-decoration: none;
            color: #495057;
            text-align: center;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .quick-link:hover {
            background: #f8f9fa;
            border-color: #667eea;
            transform: translateY(-2px);
            text-decoration: none;
            color: #495057;
        }
        
        @media (max-width: 768px) {
            .integration-flow {
                grid-template-columns: 1fr;
            }
            
            .flow-step::after {
                content: '↓';
                right: 50%;
                top: auto;
                bottom: -20px;
                transform: translateX(50%);
            }
            
            .flow-step:last-child::after {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="header">
            <h1>🔗 تكامل الأنظمة</h1>
            <p>عرض توضيحي لكيفية تكامل النظام القضائي مع النظام المحاسبي</p>
        </div>
        
        <!-- مسار التكامل -->
        <div class="integration-flow">
            <div class="flow-step">
                <span class="step-icon">⚖️</span>
                <div class="step-title">النظام القضائي</div>
                <div class="step-description">إدارة القضايا والمتابعات القانونية مع تسجيل جميع التفاصيل المالية</div>
            </div>
            
            <div class="flow-step">
                <span class="step-icon">🔄</span>
                <div class="step-title">الربط التلقائي</div>
                <div class="step-description">ربط تلقائي بين القضايا والحسابات المحاسبية مع إنشاء قيود آلية</div>
            </div>
            
            <div class="flow-step">
                <span class="step-icon">💰</span>
                <div class="step-title">النظام المحاسبي</div>
                <div class="step-description">تسجيل تلقائي للمعاملات المالية وإنشاء التقارير المحاسبية</div>
            </div>
            
            <div class="flow-step">
                <span class="step-icon">📊</span>
                <div class="step-title">التقارير المتكاملة</div>
                <div class="step-description">تقارير شاملة تجمع بين البيانات القانونية والمحاسبية</div>
            </div>
        </div>
        
        <!-- أمثلة التكامل -->
        <div class="integration-examples">
            <h3>🎯 أمثلة عملية على التكامل</h3>
            
            <div class="example-item">
                <div class="example-title">1. إنشاء قضية جديدة</div>
                <div class="example-description">
                    عند إنشاء قضية جديدة، يتم تلقائياً إنشاء حساب محاسبي للقضية وتسجيل القيود المالية المتوقعة
                </div>
                <div class="example-links">
                    <a href="/app/fms/?fm=legal-issues&cmd=add" class="example-link" target="_blank">إضافة قضية جديدة</a>
                    <a href="/app/fms/?fm=accounts&cmd=list" class="example-link" target="_blank">عرض الحسابات</a>
                </div>
            </div>
            
            <div class="example-item">
                <div class="example-title">2. متابعة القضايا</div>
                <div class="example-description">
                    كل متابعة للقضية تسجل تلقائياً في النظام المحاسبي كمصروف أو إيراد حسب نوع المتابعة
                </div>
                <div class="example-links">
                    <a href="/app/fms/?fm=case-follows&cmd=add" class="example-link" target="_blank">إضافة متابعة</a>
                    <a href="/app/fms/?fm=vouchers&cmd=list" class="example-link" target="_blank">عرض السندات</a>
                </div>
            </div>
            
            <div class="example-item">
                <div class="example-title">3. التقارير المالية للقضايا</div>
                <div class="example-description">
                    تقارير شاملة تظهر الوضع المالي لكل قضية مع ربطها بالحسابات المحاسبية
                </div>
                <div class="example-links">
                    <a href="/app/fms/?fm=legal-reports&cmd=cases-summary" class="example-link" target="_blank">تقرير القضايا</a>
                    <a href="/app/fms/?fm=financial-reports&cmd=list" class="example-link" target="_blank">التقارير المالية</a>
                </div>
            </div>
        </div>
        
        <!-- فوائد التكامل -->
        <div class="benefits-section">
            <div class="benefit-card">
                <span class="benefit-icon">⚡</span>
                <div class="benefit-title">توفير الوقت</div>
                <div class="benefit-description">إدخال البيانات مرة واحدة فقط مع تحديث تلقائي لجميع الأنظمة</div>
            </div>
            
            <div class="benefit-card">
                <span class="benefit-icon">🎯</span>
                <div class="benefit-title">دقة البيانات</div>
                <div class="benefit-description">تقليل الأخطاء البشرية من خلال الربط التلقائي بين الأنظمة</div>
            </div>
            
            <div class="benefit-card">
                <span class="benefit-icon">📈</span>
                <div class="benefit-title">تقارير شاملة</div>
                <div class="benefit-description">تقارير متكاملة تجمع البيانات القانونية والمحاسبية</div>
            </div>
            
            <div class="benefit-card">
                <span class="benefit-icon">🔒</span>
                <div class="benefit-title">أمان البيانات</div>
                <div class="benefit-description">نظام صلاحيات موحد يضمن أمان وسرية المعلومات</div>
            </div>
        </div>
        
        <!-- وصول سريع -->
        <div class="quick-access">
            <h3>🚀 وصول سريع للأنظمة</h3>
            <div class="quick-links">
                <a href="/app/main_menu.html" class="quick-link">
                    <strong>🏠 القائمة الرئيسية</strong><br>
                    <small>جميع الأنظمة المتكاملة</small>
                </a>
                <a href="/app/fms/?fm=legal-issues&cmd=list" class="quick-link">
                    <strong>⚖️ النظام القضائي</strong><br>
                    <small>إدارة القضايا والمتابعات</small>
                </a>
                <a href="/app/fms/?fm=accounts&cmd=list" class="quick-link">
                    <strong>💰 النظام المحاسبي</strong><br>
                    <small>إدارة الحسابات والمعاملات</small>
                </a>
                <a href="/app/fms/?fm=legal-reports&cmd=cases-summary" class="quick-link">
                    <strong>📊 التقارير المتكاملة</strong><br>
                    <small>تقارير شاملة للأنظمة</small>
                </a>
                <a href="/app/dev_tools.html" class="quick-link">
                    <strong>🔧 أدوات المطور</strong><br>
                    <small>أدوات التطوير والاختبار</small>
                </a>
                <a href="/app/simple_test.html" class="quick-link">
                    <strong>🧪 اختبار النظام</strong><br>
                    <small>فحص شامل للنظام</small>
                </a>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #dee2e6; color: #7f8c8d;">
            <p>© 2025 النظام المحاسبي المتكامل - تم دمج النظام القضائي بنجاح ✨</p>
            <p><strong>الإصدار:</strong> 1.0.0 | <strong>تاريخ التكامل:</strong> 2025-08-22</p>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔗 صفحة عرض تكامل الأنظمة جاهزة');
            console.log('⚖️ النظام القضائي مدمج مع النظام المحاسبي');
            console.log('💰 ربط تلقائي بين القضايا والحسابات');
            console.log('📊 تقارير متكاملة متاحة');
            
            // إضافة تأثيرات تفاعلية
            const steps = document.querySelectorAll('.flow-step');
            steps.forEach((step, index) => {
                step.addEventListener('mouseenter', function() {
                    this.style.background = '#e3f2fd';
                });
                
                step.addEventListener('mouseleave', function() {
                    this.style.background = '#f8f9fa';
                });
            });
        });
    </script>
</body>
</html>

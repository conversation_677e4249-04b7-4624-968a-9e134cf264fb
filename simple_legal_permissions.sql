-- إعداد صلاحيات النظام القضائي المبسط
-- تاريخ الإنشاء: 2025-08-21

-- إدراج الصلاحيات في جدول الصلاحيات الموجود
INSERT INTO permissions (name, description, module) VALUES
-- صلاحيات عامة للنظام القضائي
('legal_system_access', 'الوصول للنظام القضائي', 'legal'),

-- صلاحيات إدارة القضايا
('legal_cases_view', 'عرض القضايا', 'legal'),
('legal_cases_add', 'إضافة قضايا جديدة', 'legal'),
('legal_cases_edit', 'تعديل القضايا', 'legal'),
('legal_cases_delete', 'حذف القضايا', 'legal'),
('legal_cases_print', 'طباعة القضايا', 'legal'),

-- صلاحيات متابعة القضايا
('legal_follows_view', 'عرض المتابعات', 'legal'),
('legal_follows_add', 'إضافة متابعات', 'legal'),
('legal_follows_edit', 'تعديل المتابعات', 'legal'),
('legal_follows_delete', 'حذف المتابعات', 'legal'),

-- صلاحيات التقارير القانونية
('legal_reports_view', 'عرض التقارير القانونية', 'legal'),
('legal_reports_export', 'تصدير التقارير', 'legal'),
('legal_reports_print', 'طباعة التقارير', 'legal'),

-- صلاحيات الإعدادات القانونية
('legal_settings_view', 'عرض الإعدادات القانونية', 'legal'),
('legal_settings_edit', 'تعديل الإعدادات القانونية', 'legal'),

-- صلاحيات إدارية متقدمة
('legal_admin_full', 'إدارة كاملة للنظام القضائي', 'legal')

ON CONFLICT (name) DO NOTHING;

-- إنشاء أدوار قانونية في جدول الأدوار الموجود
INSERT INTO roles (name, description) VALUES
('legal_admin', 'مدير النظام القضائي'),
('legal_lawyer', 'محامي'),
('legal_secretary', 'سكرتير قانوني'),
('legal_viewer', 'مشاهد قانوني'),
('legal_accountant', 'محاسب قانوني')
ON CONFLICT (name) DO NOTHING;

-- ربط صلاحيات المدير القانوني (جميع الصلاحيات)
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'legal_admin' LIMIT 1),
    p.id
FROM permissions p
WHERE p.module = 'legal'
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المحامي
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'legal_lawyer' LIMIT 1),
    p.id
FROM permissions p
WHERE p.name IN (
    'legal_system_access',
    'legal_cases_view',
    'legal_cases_add',
    'legal_cases_edit',
    'legal_cases_print',
    'legal_follows_view',
    'legal_follows_add',
    'legal_follows_edit',
    'legal_reports_view',
    'legal_reports_print',
    'legal_settings_view'
)
ON CONFLICT DO NOTHING;

-- ربط صلاحيات السكرتير القانوني
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'legal_secretary' LIMIT 1),
    p.id
FROM permissions p
WHERE p.name IN (
    'legal_system_access',
    'legal_cases_view',
    'legal_cases_add',
    'legal_follows_view',
    'legal_follows_add',
    'legal_follows_edit',
    'legal_reports_view'
)
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المشاهد القانوني
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'legal_viewer' LIMIT 1),
    p.id
FROM permissions p
WHERE p.name IN (
    'legal_system_access',
    'legal_cases_view',
    'legal_follows_view',
    'legal_reports_view'
)
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المحاسب القانوني
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'legal_accountant' LIMIT 1),
    p.id
FROM permissions p
WHERE p.name IN (
    'legal_system_access',
    'legal_cases_view',
    'legal_follows_view',
    'legal_reports_view',
    'legal_reports_export',
    'legal_reports_print'
)
ON CONFLICT DO NOTHING;

-- إعطاء صلاحيات النظام القضائي للمدير العام (admin)
INSERT INTO role_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'admin' LIMIT 1),
    p.id
FROM permissions p
WHERE p.module = 'legal'
ON CONFLICT DO NOTHING;

-- التحقق من نجاح الإعداد
SELECT 
    'تم إعداد صلاحيات النظام القضائي بنجاح' as الحالة,
    COUNT(*) as عدد_الصلاحيات_المضافة
FROM permissions 
WHERE module = 'legal';

-- عرض ملخص الأدوار والصلاحيات
SELECT 
    r.name as الدور,
    COUNT(rp.permission_id) as عدد_الصلاحيات
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
WHERE r.name LIKE 'legal_%' OR p.module = 'legal'
GROUP BY r.id, r.name
ORDER BY r.name;

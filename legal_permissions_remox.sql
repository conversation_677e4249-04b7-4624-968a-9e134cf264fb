-- إعد<PERSON> صلاحيات النظام القضائي لنظام RemoX
-- تاريخ الإنشاء: 2025-08-21

-- إن<PERSON>اء جدول الصلاحيات إذا لم يكن موجوداً (متوافق مع RemoX)
CREATE TABLE IF NOT EXISTS system_permissions (
    id SERIAL PRIMARY KEY,
    permission_code VARCHAR(100) UNIQUE NOT NULL,
    permission_name VARCHAR(200) NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إ<PERSON><PERSON><PERSON><PERSON> جدول ربط المستخدمين بالصلاحيات
CREATE TABLE IF NOT EXISTS user_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    permission_code VARCHAR(100) NOT NULL,
    granted_by INTEGER,
    granted_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (permission_code) REFERENCES system_permissions(permission_code)
);

-- إنشاء جدول الأدوار
CREATE TABLE IF NOT EXISTS user_roles (
    id SERIAL PRIMARY KEY,
    role_code VARCHAR(50) UNIQUE NOT NULL,
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول ربط الأدوار بالصلاحيات
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_code VARCHAR(50) NOT NULL,
    permission_code VARCHAR(100) NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (role_code) REFERENCES user_roles(role_code),
    FOREIGN KEY (permission_code) REFERENCES system_permissions(permission_code)
);

-- إنشاء جدول ربط المستخدمين بالأدوار
CREATE TABLE IF NOT EXISTS user_role_assignments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    role_code VARCHAR(50) NOT NULL,
    assigned_by INTEGER,
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (role_code) REFERENCES user_roles(role_code)
);

-- إدراج صلاحيات النظام القضائي
INSERT INTO system_permissions (permission_code, permission_name, module_name, description) VALUES
-- صلاحيات عامة للنظام القضائي
('legal_system_access', 'الوصول للنظام القضائي', 'legal', 'صلاحية الوصول الأساسية للنظام القضائي'),

-- صلاحيات إدارة القضايا
('legal_cases_view', 'عرض القضايا', 'legal', 'عرض قائمة القضايا وتفاصيلها'),
('legal_cases_add', 'إضافة قضايا جديدة', 'legal', 'إضافة قضايا جديدة للنظام'),
('legal_cases_edit', 'تعديل القضايا', 'legal', 'تعديل بيانات القضايا الموجودة'),
('legal_cases_delete', 'حذف القضايا', 'legal', 'حذف القضايا من النظام'),
('legal_cases_print', 'طباعة القضايا', 'legal', 'طباعة تقارير وبيانات القضايا'),

-- صلاحيات متابعة القضايا
('legal_follows_view', 'عرض المتابعات', 'legal', 'عرض قائمة متابعات القضايا'),
('legal_follows_add', 'إضافة متابعات', 'legal', 'إضافة متابعات جديدة للقضايا'),
('legal_follows_edit', 'تعديل المتابعات', 'legal', 'تعديل بيانات المتابعات'),
('legal_follows_delete', 'حذف المتابعات', 'legal', 'حذف المتابعات من النظام'),

-- صلاحيات التقارير القانونية
('legal_reports_view', 'عرض التقارير القانونية', 'legal', 'عرض وإنشاء التقارير القانونية'),
('legal_reports_export', 'تصدير التقارير', 'legal', 'تصدير التقارير بصيغ مختلفة'),
('legal_reports_print', 'طباعة التقارير', 'legal', 'طباعة التقارير القانونية'),

-- صلاحيات الإعدادات القانونية
('legal_settings_view', 'عرض الإعدادات القانونية', 'legal', 'عرض إعدادات النظام القضائي'),
('legal_settings_edit', 'تعديل الإعدادات القانونية', 'legal', 'تعديل إعدادات النظام القضائي'),

-- صلاحيات إدارية متقدمة
('legal_admin_full', 'إدارة كاملة للنظام القضائي', 'legal', 'صلاحيات إدارية كاملة للنظام القضائي'),
('legal_backup_restore', 'النسخ الاحتياطي والاستعادة', 'legal', 'إنشاء واستعادة النسخ الاحتياطية'),
('legal_user_management', 'إدارة مستخدمي النظام القضائي', 'legal', 'إدارة صلاحيات المستخدمين للنظام القضائي')

ON CONFLICT (permission_code) DO NOTHING;

-- إنشاء الأدوار الأساسية
INSERT INTO user_roles (role_code, role_name, description) VALUES
('legal_admin', 'مدير النظام القضائي', 'مدير عام للنظام القضائي مع جميع الصلاحيات'),
('legal_lawyer', 'محامي', 'محامي مع صلاحيات إدارة القضايا والمتابعات'),
('legal_secretary', 'سكرتير قانوني', 'سكرتير مع صلاحيات محدودة للمتابعة والعرض'),
('legal_viewer', 'مشاهد قانوني', 'مستخدم مع صلاحيات العرض فقط'),
('legal_accountant', 'محاسب قانوني', 'محاسب مع صلاحيات التقارير المالية القانونية')

ON CONFLICT (role_code) DO NOTHING;

-- ربط صلاحيات المدير القانوني (جميع الصلاحيات)
INSERT INTO role_permissions (role_code, permission_code) 
SELECT 'legal_admin', permission_code 
FROM system_permissions 
WHERE module_name = 'legal'
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المحامي
INSERT INTO role_permissions (role_code, permission_code) VALUES
('legal_lawyer', 'legal_system_access'),
('legal_lawyer', 'legal_cases_view'),
('legal_lawyer', 'legal_cases_add'),
('legal_lawyer', 'legal_cases_edit'),
('legal_lawyer', 'legal_cases_print'),
('legal_lawyer', 'legal_follows_view'),
('legal_lawyer', 'legal_follows_add'),
('legal_lawyer', 'legal_follows_edit'),
('legal_lawyer', 'legal_reports_view'),
('legal_lawyer', 'legal_reports_print'),
('legal_lawyer', 'legal_settings_view')
ON CONFLICT DO NOTHING;

-- ربط صلاحيات السكرتير القانوني
INSERT INTO role_permissions (role_code, permission_code) VALUES
('legal_secretary', 'legal_system_access'),
('legal_secretary', 'legal_cases_view'),
('legal_secretary', 'legal_cases_add'),
('legal_secretary', 'legal_follows_view'),
('legal_secretary', 'legal_follows_add'),
('legal_secretary', 'legal_follows_edit'),
('legal_secretary', 'legal_reports_view')
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المشاهد القانوني
INSERT INTO role_permissions (role_code, permission_code) VALUES
('legal_viewer', 'legal_system_access'),
('legal_viewer', 'legal_cases_view'),
('legal_viewer', 'legal_follows_view'),
('legal_viewer', 'legal_reports_view')
ON CONFLICT DO NOTHING;

-- ربط صلاحيات المحاسب القانوني
INSERT INTO role_permissions (role_code, permission_code) VALUES
('legal_accountant', 'legal_system_access'),
('legal_accountant', 'legal_cases_view'),
('legal_accountant', 'legal_follows_view'),
('legal_accountant', 'legal_reports_view'),
('legal_accountant', 'legal_reports_export'),
('legal_accountant', 'legal_reports_print')
ON CONFLICT DO NOTHING;

-- إعطاء صلاحيات المدير العام (admin) جميع صلاحيات النظام القضائي
INSERT INTO user_permissions (user_id, permission_code, granted_by)
SELECT 1, permission_code, 1
FROM system_permissions 
WHERE module_name = 'legal'
ON CONFLICT DO NOTHING;

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_code ON user_permissions(permission_code);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_code ON role_permissions(role_code);
CREATE INDEX IF NOT EXISTS idx_user_role_assignments_user_id ON user_role_assignments(user_id);

-- إنشاء دالة للتحقق من صلاحيات المستخدم
CREATE OR REPLACE FUNCTION check_user_permission(p_user_id INTEGER, p_permission_code VARCHAR)
RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN := FALSE;
BEGIN
    -- التحقق من الصلاحية المباشرة
    SELECT EXISTS(
        SELECT 1 FROM user_permissions 
        WHERE user_id = p_user_id 
        AND permission_code = p_permission_code 
        AND is_active = TRUE
    ) INTO has_permission;
    
    -- إذا لم توجد صلاحية مباشرة، تحقق من الأدوار
    IF NOT has_permission THEN
        SELECT EXISTS(
            SELECT 1 FROM user_role_assignments ura
            JOIN role_permissions rp ON ura.role_code = rp.role_code
            WHERE ura.user_id = p_user_id 
            AND rp.permission_code = p_permission_code
            AND ura.is_active = TRUE
        ) INTO has_permission;
    END IF;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لإعطاء دور لمستخدم
CREATE OR REPLACE FUNCTION assign_user_role(p_user_id INTEGER, p_role_code VARCHAR, p_assigned_by INTEGER DEFAULT 1)
RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO user_role_assignments (user_id, role_code, assigned_by)
    VALUES (p_user_id, p_role_code, p_assigned_by)
    ON CONFLICT DO NOTHING;
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- التحقق من نجاح الإعداد
SELECT 
    'تم إعداد النظام القضائي بنجاح' as الحالة,
    COUNT(*) as عدد_الصلاحيات
FROM system_permissions 
WHERE module_name = 'legal';

-- عرض ملخص الأدوار والصلاحيات
SELECT 
    ur.role_name as الدور,
    COUNT(rp.permission_code) as عدد_الصلاحيات
FROM user_roles ur
LEFT JOIN role_permissions rp ON ur.role_code = rp.role_code
WHERE ur.role_code LIKE 'legal_%'
GROUP BY ur.role_code, ur.role_name
ORDER BY ur.role_name;

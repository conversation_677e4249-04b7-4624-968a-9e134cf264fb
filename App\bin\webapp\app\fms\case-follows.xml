<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج متابعة القضايا - متوافق مع نظام RemoX -->
<form-module name="case-follows" title="متابعة القضايا" icon="calendar"
             permission="legal_follows_view" add-permission="legal_follows_add"
             edit-permission="legal_follows_edit" delete-permission="legal_follows_delete">

    <!-- قائمة المتابعات -->
    <list-view name="list" title="قائمة المتابعات" permission="legal_follows_view">
        <data-source>
            SELECT
                f.id,
                i.case_number,
                i.title as case_title,
                i.client_name,
                COALESCE(s.name, 'خدمة غير محددة') as service_name,
                f.report,
                f.date_field,
                f.status,
                COALESCE(e.name, u.username, 'غير محدد') as user_name,
                f.created_date,
                i.amount as case_amount,
                i.contract_method
            FROM follows f
            JOIN issues i ON f.case_id = i.id
            LEFT JOIN services s ON f.service_id = s.id
            LEFT JOIN employees e ON f.user_id = e.id
            LEFT JOIN users u ON f.user_id = u.id
            ORDER BY f.date_field DESC, f.created_date DESC
        </data-source>

        <columns>
            <column name="case_number" title="رقم القضية" width="120" sortable="true" searchable="true"/>
            <column name="case_title" title="عنوان القضية" width="200" sortable="true" searchable="true"/>
            <column name="client_name" title="الموكل" width="120" searchable="true"/>
            <column name="service_name" title="نوع الخدمة" width="150" filter="true"/>
            <column name="report" title="تقرير المتابعة" width="300" truncate="80" searchable="true"/>
            <column name="date_field" title="تاريخ المتابعة" width="120" format="date" sortable="true"/>
            <column name="status" title="الحالة" width="100" format="status" filter="true"/>
            <column name="user_name" title="المستخدم" width="120" filter="true"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="datetime"/>
        </columns>

        <filters>
            <filter name="case_id" title="القضية" type="lookup"
                   lookup-table="issues" lookup-field="case_number + ' - ' + title" lookup-value="id"/>
            <filter name="service_id" title="نوع الخدمة" type="lookup"
                   lookup-table="services" lookup-field="name" lookup-value="id"/>
            <filter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </filter>
            <filter name="user_id" title="المستخدم" type="lookup"
                   lookup-table="employees" lookup-field="name" lookup-value="id"/>
            <filter name="date_from" title="من تاريخ" type="date" field="date_field"/>
            <filter name="date_to" title="إلى تاريخ" type="date" field="date_field"/>
        </filters>

        <actions>
            <action name="add" title="إضافة متابعة جديدة" icon="plus" color="primary"
                   permission="legal_follows_add"/>
            <action name="edit" title="تعديل" icon="edit" color="warning"
                   permission="legal_follows_edit"/>
            <action name="delete" title="حذف" icon="delete" color="danger"
                   permission="legal_follows_delete" confirm="true"/>
            <action name="view" title="عرض التفاصيل" icon="eye" color="info"/>
            <action name="case_details" title="تفاصيل القضية" icon="gavel" color="success"
                   url="/app/fms/?fm=legal-issues&amp;cmd=view&amp;id={case_id}"/>
            <action name="print" title="طباعة" icon="print" color="secondary"
                   permission="legal_follows_view"/>
        </actions>

        <summary>
            <field name="total_follows" title="إجمالي المتابعات" type="count"/>
            <field name="completed_follows" title="المتابعات المكتملة" type="count" condition="status = 'completed'"/>
            <field name="pending_follows" title="المتابعات المعلقة" type="count" condition="status = 'pending'"/>
            <field name="today_follows" title="متابعات اليوم" type="count" condition="DATE(date_field) = CURRENT_DATE"/>
        </summary>
    </list-view>

    <!-- نموذج إضافة متابعة جديدة -->
    <form-view name="add" title="إضافة متابعة جديدة" permission="legal_follows_add">
        <fields>
            <field name="case_id" title="القضية" type="lookup" required="true"
                   lookup-table="issues" lookup-field="case_number + ' - ' + title" lookup-value="id"
                   lookup-display="case_number + ' - ' + title + ' (' + client_name + ')'"
                   help="اختر القضية المراد متابعتها"/>
            <field name="service_id" title="نوع الخدمة" type="lookup" required="true"
                   lookup-table="services" lookup-field="name" lookup-value="id"
                   lookup-filter="is_active = true"
                   help="نوع الخدمة المقدمة في هذه المتابعة"/>
            <field name="report" title="تقرير المتابعة" type="textarea" required="true" rows="6"
                   placeholder="اكتب تقرير مفصل عن المتابعة..."
                   maxlength="2000" help="تقرير تفصيلي عن إجراءات المتابعة"/>
            <field name="date_field" title="تاريخ المتابعة" type="date" default="today" required="true"
                   help="تاريخ تنفيذ المتابعة"/>
            <field name="status" title="حالة المتابعة" type="select" default="pending" required="true">
                <options>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </field>
            <field name="notes" title="ملاحظات إضافية" type="textarea" rows="3"
                   placeholder="ملاحظات أو تعليقات إضافية..."
                   maxlength="500"/>
        </fields>

        <save-action>
            INSERT INTO follows (
                case_id, service_id, user_id, report, date_field, status, notes, created_date
            ) VALUES (
                @case_id, @service_id, @current_user_id, @report, @date_field, @status, @notes, CURRENT_TIMESTAMP
            )
        </save-action>

        <validation>
            <rule field="case_id" type="required" message="يجب اختيار القضية"/>
            <rule field="service_id" type="required" message="يجب اختيار نوع الخدمة"/>
            <rule field="report" type="required" message="تقرير المتابعة مطلوب"/>
            <rule field="date_field" type="required" message="تاريخ المتابعة مطلوب"/>
        </validation>
    </form-view>

    <!-- نموذج تعديل المتابعة -->
    <form-view name="edit" title="تعديل المتابعة" permission="legal_follows_edit">
        <load-action>
            SELECT
                f.*,
                i.case_number + ' - ' + i.title as case_display,
                s.name as service_name
            FROM follows f
            JOIN issues i ON f.case_id = i.id
            LEFT JOIN services s ON f.service_id = s.id
            WHERE f.id = @id
        </load-action>

        <fields>
            <field name="case_display" title="القضية" type="text" readonly="true"/>
            <field name="service_id" title="نوع الخدمة" type="lookup" required="true"
                   lookup-table="services" lookup-field="name" lookup-value="id"/>
            <field name="report" title="تقرير المتابعة" type="textarea" required="true" rows="6"/>
            <field name="date_field" title="تاريخ المتابعة" type="date" required="true"/>
            <field name="status" title="حالة المتابعة" type="select" required="true">
                <options>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </field>
            <field name="notes" title="ملاحظات إضافية" type="textarea" rows="3"/>
        </fields>

        <update-action>
            UPDATE follows SET
                service_id = @service_id,
                report = @report,
                date_field = @date_field,
                status = @status,
                notes = @notes,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = @id
        </update-action>
    </form-view>

    <!-- نموذج عرض تفاصيل المتابعة -->
    <form-view name="view" title="تفاصيل المتابعة" permission="legal_follows_view" readonly="true">
        <load-action>
            SELECT
                f.*,
                i.case_number,
                i.title as case_title,
                i.client_name,
                i.amount as case_amount,
                s.name as service_name,
                s.description as service_description,
                COALESCE(e.name, u.username) as user_name
            FROM follows f
            JOIN issues i ON f.case_id = i.id
            LEFT JOIN services s ON f.service_id = s.id
            LEFT JOIN employees e ON f.user_id = e.id
            LEFT JOIN users u ON f.user_id = u.id
            WHERE f.id = @id
        </load-action>

        <tabs>
            <tab name="details" title="تفاصيل المتابعة" icon="info">
                <fields>
                    <field name="case_number" title="رقم القضية" type="text" readonly="true"/>
                    <field name="case_title" title="عنوان القضية" type="text" readonly="true"/>
                    <field name="client_name" title="الموكل" type="text" readonly="true"/>
                    <field name="service_name" title="نوع الخدمة" type="text" readonly="true"/>
                    <field name="report" title="تقرير المتابعة" type="textarea" readonly="true"/>
                    <field name="date_field" title="تاريخ المتابعة" type="date" readonly="true"/>
                    <field name="status" title="الحالة" type="text" readonly="true"/>
                    <field name="user_name" title="المستخدم" type="text" readonly="true"/>
                    <field name="created_date" title="تاريخ الإنشاء" type="datetime" readonly="true"/>
                </fields>
            </tab>

            <tab name="case_info" title="معلومات القضية" icon="gavel">
                <fields>
                    <field name="case_amount" title="مبلغ القضية" type="decimal" format="currency" readonly="true"/>
                    <field name="service_description" title="وصف الخدمة" type="textarea" readonly="true"/>
                </fields>

                <actions>
                    <action name="view_case" title="عرض تفاصيل القضية" icon="gavel" color="primary"
                           url="/app/fms/?fm=legal-issues&amp;cmd=view&amp;id={case_id}"/>
                </actions>
            </tab>
        </tabs>
    </form-view>
</form-module>

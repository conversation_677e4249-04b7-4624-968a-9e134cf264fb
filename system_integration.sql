-- ربط النظام القضائي بالنظام المحاسبي
-- تاريخ الإنشاء: 2025-08-22

-- إن<PERSON>اء جدول ربط الأنظمة
CREATE TABLE IF NOT EXISTS system_modules (
    id SERIAL PRIMARY KEY,
    module_code VARCHAR(50) UNIQUE NOT NULL,
    module_name VARCHAR(100) NOT NULL,
    module_description TEXT,
    module_icon VARCHAR(50),
    module_color VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدراج الأنظمة المتكاملة
INSERT INTO system_modules (module_code, module_name, module_description, module_icon, module_color, sort_order) VALUES
('accounting', 'النظام المحاسبي', 'إدارة شاملة للحسابات والمعاملات المالية', '💰', '#3498db', 1),
('legal', 'النظام القضائي', 'إدارة متكاملة للقضايا والمتابعات القانونية', '⚖️', '#e74c3c', 2),
('hr', 'الموارد البشرية', 'إدارة شاملة للموظفين والرواتب', '👥', '#f39c12', 3),
('inventory', 'إدارة المخازن', 'متابعة المخزون والمشتريات والمبيعات', '📦', '#27ae60', 4),
('reports', 'التقارير والتحليلات', 'تقارير شاملة ومتقدمة لجميع الأنظمة', '📊', '#9b59b6', 5),
('settings', 'الإعدادات العامة', 'إعدادات النظام والصلاحيات', '⚙️', '#34495e', 6)
ON CONFLICT (module_code) DO UPDATE SET
    module_name = EXCLUDED.module_name,
    module_description = EXCLUDED.module_description,
    module_icon = EXCLUDED.module_icon,
    module_color = EXCLUDED.module_color,
    sort_order = EXCLUDED.sort_order;

-- إنشاء جدول ربط القضايا بالحسابات المحاسبية
CREATE TABLE IF NOT EXISTS legal_accounting_links (
    id SERIAL PRIMARY KEY,
    case_id INTEGER NOT NULL,
    account_id INTEGER,
    voucher_id INTEGER,
    link_type VARCHAR(50) NOT NULL, -- 'revenue', 'expense', 'asset', 'liability'
    amount DECIMAL(15,2),
    description TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    FOREIGN KEY (case_id) REFERENCES issues(id),
    INDEX idx_case_accounting (case_id, link_type)
);

-- إنشاء جدول للقيود المحاسبية التلقائية للقضايا
CREATE TABLE IF NOT EXISTS legal_auto_entries (
    id SERIAL PRIMARY KEY,
    case_id INTEGER NOT NULL,
    entry_type VARCHAR(50) NOT NULL, -- 'case_creation', 'follow_completion', 'case_closure'
    debit_account_id INTEGER,
    credit_account_id INTEGER,
    amount DECIMAL(15,2),
    description TEXT,
    is_processed BOOLEAN DEFAULT FALSE,
    processed_date TIMESTAMP,
    voucher_id INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (case_id) REFERENCES issues(id)
);

-- إنشاء حسابات محاسبية خاصة بالنظام القضائي
INSERT INTO accounts (account_code, account_name, account_type, parent_id, is_active) VALUES
('4100', 'إيرادات الخدمات القانونية', 'revenue', NULL, TRUE),
('4101', 'أتعاب المحاماة', 'revenue', (SELECT id FROM accounts WHERE account_code = '4100' LIMIT 1), TRUE),
('4102', 'رسوم الاستشارات القانونية', 'revenue', (SELECT id FROM accounts WHERE account_code = '4100' LIMIT 1), TRUE),
('4103', 'أتعاب المتابعات القضائية', 'revenue', (SELECT id FROM accounts WHERE account_code = '4100' LIMIT 1), TRUE),

('5200', 'مصروفات الخدمات القانونية', 'expense', NULL, TRUE),
('5201', 'رسوم المحاكم', 'expense', (SELECT id FROM accounts WHERE account_code = '5200' LIMIT 1), TRUE),
('5202', 'مصروفات التنقل للقضايا', 'expense', (SELECT id FROM accounts WHERE account_code = '5200' LIMIT 1), TRUE),
('5203', 'رسوم الخبراء والمترجمين', 'expense', (SELECT id FROM accounts WHERE account_code = '5200' LIMIT 1), TRUE),

('1300', 'أصول القضايا', 'asset', NULL, TRUE),
('1301', 'مستحقات أتعاب قضايا', 'asset', (SELECT id FROM accounts WHERE account_code = '1300' LIMIT 1), TRUE),
('1302', 'سلف على قضايا', 'asset', (SELECT id FROM accounts WHERE account_code = '1300' LIMIT 1), TRUE)

ON CONFLICT (account_code) DO NOTHING;

-- إنشاء دالة لإنشاء قيد محاسبي تلقائي عند إنشاء قضية
CREATE OR REPLACE FUNCTION create_case_accounting_entry(
    p_case_id INTEGER,
    p_amount DECIMAL(15,2),
    p_description TEXT DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
    v_voucher_id INTEGER;
    v_debit_account INTEGER;
    v_credit_account INTEGER;
    v_description TEXT;
BEGIN
    -- الحصول على الحسابات المحاسبية
    SELECT id INTO v_debit_account FROM accounts WHERE account_code = '1301' LIMIT 1; -- مستحقات أتعاب
    SELECT id INTO v_credit_account FROM accounts WHERE account_code = '4101' LIMIT 1; -- أتعاب المحاماة
    
    -- تحديد الوصف
    v_description := COALESCE(p_description, 'قيد تلقائي للقضية رقم ' || p_case_id);
    
    -- إنشاء سند محاسبي
    INSERT INTO vouchers (voucher_type, voucher_date, description, total_amount, created_date)
    VALUES ('journal', CURRENT_DATE, v_description, p_amount, CURRENT_TIMESTAMP)
    RETURNING id INTO v_voucher_id;
    
    -- إنشاء قيود السند
    INSERT INTO voucher_entries (voucher_id, account_id, debit_amount, credit_amount, description)
    VALUES 
    (v_voucher_id, v_debit_account, p_amount, 0, v_description),
    (v_voucher_id, v_credit_account, 0, p_amount, v_description);
    
    -- ربط القضية بالسند
    INSERT INTO legal_accounting_links (case_id, voucher_id, link_type, amount, description)
    VALUES (p_case_id, v_voucher_id, 'revenue', p_amount, v_description);
    
    RETURN v_voucher_id;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لإنشاء قيد عند اكتمال متابعة
CREATE OR REPLACE FUNCTION complete_follow_accounting_entry(
    p_follow_id INTEGER,
    p_amount DECIMAL(15,2) DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
    v_case_id INTEGER;
    v_voucher_id INTEGER;
    v_amount DECIMAL(15,2);
    v_description TEXT;
BEGIN
    -- الحصول على معرف القضية
    SELECT case_id INTO v_case_id FROM follows WHERE id = p_follow_id;
    
    -- تحديد المبلغ (إذا لم يتم تمريره)
    IF p_amount IS NULL THEN
        SELECT amount * 0.1 INTO v_amount FROM issues WHERE id = v_case_id; -- 10% من قيمة القضية
    ELSE
        v_amount := p_amount;
    END IF;
    
    v_description := 'قيد اكتمال متابعة رقم ' || p_follow_id || ' للقضية رقم ' || v_case_id;
    
    -- إنشاء القيد المحاسبي
    SELECT create_case_accounting_entry(v_case_id, v_amount, v_description) INTO v_voucher_id;
    
    RETURN v_voucher_id;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشغل (Trigger) لإنشاء قيود تلقائية
CREATE OR REPLACE FUNCTION trigger_case_accounting() RETURNS TRIGGER AS $$
BEGIN
    -- عند إنشاء قضية جديدة
    IF TG_OP = 'INSERT' AND NEW.amount > 0 THEN
        INSERT INTO legal_auto_entries (case_id, entry_type, amount, description)
        VALUES (NEW.id, 'case_creation', NEW.amount, 'قيد تلقائي لإنشاء القضية');
    END IF;
    
    -- عند تحديث حالة القضية إلى مكتملة
    IF TG_OP = 'UPDATE' AND OLD.status != 'completed' AND NEW.status = 'completed' THEN
        INSERT INTO legal_auto_entries (case_id, entry_type, amount, description)
        VALUES (NEW.id, 'case_closure', NEW.amount, 'قيد تلقائي لإغلاق القضية');
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ربط المشغل بجدول القضايا
DROP TRIGGER IF EXISTS trigger_case_accounting_insert ON issues;
CREATE TRIGGER trigger_case_accounting_insert
    AFTER INSERT OR UPDATE ON issues
    FOR EACH ROW
    EXECUTE FUNCTION trigger_case_accounting();

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_legal_accounting_case_id ON legal_accounting_links(case_id);
CREATE INDEX IF NOT EXISTS idx_legal_auto_entries_case_id ON legal_auto_entries(case_id);
CREATE INDEX IF NOT EXISTS idx_legal_auto_entries_processed ON legal_auto_entries(is_processed);

-- إنشاء view للتقارير المتكاملة
CREATE OR REPLACE VIEW legal_financial_summary AS
SELECT 
    i.id as case_id,
    i.case_number,
    i.title,
    i.client_name,
    i.amount as case_amount,
    i.status,
    COUNT(f.id) as follows_count,
    SUM(CASE WHEN f.status = 'completed' THEN 1 ELSE 0 END) as completed_follows,
    COALESCE(SUM(lal.amount), 0) as total_accounting_amount,
    COUNT(lal.id) as accounting_entries_count,
    i.created_date
FROM issues i
LEFT JOIN follows f ON i.id = f.case_id
LEFT JOIN legal_accounting_links lal ON i.id = lal.case_id
GROUP BY i.id, i.case_number, i.title, i.client_name, i.amount, i.status, i.created_date;

-- التحقق من نجاح الدمج
SELECT 
    'تم دمج النظام القضائي مع النظام المحاسبي بنجاح' as الحالة,
    COUNT(*) as عدد_الوحدات_المدمجة
FROM system_modules 
WHERE is_active = TRUE;

-- عرض ملخص الدمج
SELECT 
    module_name as اسم_النظام,
    module_description as الوصف,
    module_icon as الأيقونة,
    sort_order as الترتيب
FROM system_modules 
WHERE is_active = TRUE
ORDER BY sort_order;

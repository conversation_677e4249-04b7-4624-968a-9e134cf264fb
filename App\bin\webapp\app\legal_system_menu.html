<!DOCTYPE html>
<html>
<head>
    <title>النظام القضائي - RemoX</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
            margin: 0;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .menu-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .menu-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .menu-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .menu-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }

        .cases-management .menu-icon { color: #e74c3c; }
        .follows-management .menu-icon { color: #f39c12; }
        .reports-section .menu-icon { color: #27ae60; }
        .settings-section .menu-icon { color: #3498db; }

        .menu-card h3 {
            color: #2c3e50;
            font-size: 1.4em;
            margin-bottom: 10px;
        }

        .menu-card p {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .menu-btn {
            display: inline-block;
            padding: 10px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 5px;
        }

        .menu-btn:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: scale(1.05);
            text-decoration: none;
            color: white;
        }

        .menu-btn.secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        }

        .menu-btn.secondary:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
        }

        .stats-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            display: block;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .breadcrumb {
            background: #ecf0f1;
            padding: 10px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .breadcrumb a {
            color: #3498db;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="breadcrumb">
            <a href="/">الرئيسية</a> / <a href="main_menu.html">النظام المتكامل</a> / النظام القضائي
        </div>

        <div class="header">
            <h1>⚖️ النظام القضائي</h1>
            <p>إدارة شاملة للقضايا والمتابعات القانونية مع التكامل المحاسبي</p>
        </div>

        <div class="menu-grid">
            <!-- إدارة القضايا -->
            <div class="menu-card cases-management">
                <span class="menu-icon">⚖️</span>
                <h3>إدارة القضايا</h3>
                <p>إضافة وتعديل ومتابعة القضايا مع جميع التفاصيل القانونية والمالية</p>
                <a href="/app/fms/?fm=legal-issues&cmd=list" class="menu-btn">قائمة القضايا</a>
                <a href="/app/fms/?fm=legal-issues&cmd=add" class="menu-btn secondary">إضافة قضية جديدة</a>
            </div>

            <!-- متابعة القضايا -->
            <div class="menu-card follows-management">
                <span class="menu-icon">📅</span>
                <h3>متابعة القضايا</h3>
                <p>إدارة متابعات القضايا والخدمات المقدمة مع تقارير مفصلة</p>
                <a href="/app/fms/?fm=case-follows&cmd=list" class="menu-btn">قائمة المتابعات</a>
                <a href="/app/fms/?fm=case-follows&cmd=add" class="menu-btn secondary">إضافة متابعة جديدة</a>
            </div>

            <!-- التقارير القانونية -->
            <div class="menu-card reports-section">
                <span class="menu-icon">📊</span>
                <h3>التقارير القانونية</h3>
                <p>تقارير شاملة للقضايا والمتابعات مع إحصائيات متقدمة</p>
                <a href="/app/fms/?fm=legal-reports&cmd=cases-summary" class="menu-btn">تقرير ملخص القضايا</a>
                <a href="/app/fms/?fm=legal-reports&cmd=follows-report" class="menu-btn secondary">تقرير المتابعات</a>
            </div>

            <!-- الإعدادات القانونية -->
            <div class="menu-card settings-section">
                <span class="menu-icon">⚙️</span>
                <h3>الإعدادات القانونية</h3>
                <p>إدارة أنواع القضايا والخدمات والإعدادات الأساسية</p>
                <a href="/app/fms/?fm=issue-types&cmd=list" class="menu-btn">أنواع القضايا</a>
                <a href="/app/fms/?fm=legal-services&cmd=list" class="menu-btn secondary">إدارة الخدمات</a>
            </div>
        </div>

        <!-- قسم الإحصائيات -->
        <div class="stats-section">
            <h3 style="text-align: center; color: #2c3e50; margin-bottom: 25px;">📈 إحصائيات سريعة</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number" id="total-cases">0</span>
                    <div class="stat-label">إجمالي القضايا</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="active-cases">0</span>
                    <div class="stat-label">القضايا النشطة</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="pending-follows">0</span>
                    <div class="stat-label">المتابعات المعلقة</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="completed-follows">0</span>
                    <div class="stat-label">المتابعات المكتملة</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="total-amount">0</span>
                    <div class="stat-label">إجمالي المبالغ</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="issue-types">0</span>
                    <div class="stat-label">أنواع القضايا</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 النظام القضائي المتكامل - جميع الحقوق محفوظة</p>
            <p>تم التطوير والدمج مع النظام المحاسبي بنجاح ✨</p>
        </div>
    </div>

    <script>
        // تحميل الإحصائيات (يمكن ربطها بـ APIs حقيقية لاحقاً)
        document.addEventListener('DOMContentLoaded', function() {
            // إحصائيات وهمية للعرض - يمكن استبدالها بـ AJAX calls حقيقية
            document.getElementById('total-cases').textContent = '25';
            document.getElementById('active-cases').textContent = '12';
            document.getElementById('pending-follows').textContent = '8';
            document.getElementById('completed-follows').textContent = '35';
            document.getElementById('total-amount').textContent = '2.5M';
            document.getElementById('issue-types').textContent = '6';

            console.log('✅ النظام القضائي جاهز');
            console.log('📋 النماذج المتاحة:');
            console.log('   - legal-cases: إدارة القضايا');
            console.log('   - case-follows: متابعة القضايا');
            console.log('   - issue-types: أنواع القضايا');
            console.log('   - legal-services: الخدمات القانونية');
            console.log('   - legal-reports: التقارير القانونية');
        });
    </script>
</body>
</html>

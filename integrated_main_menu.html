<!DOCTYPE html>
<html>
<head>
    <title>النظام المحاسبي المتكامل - RemoX</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
            margin: 0;
        }
        
        .systems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .system-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.4s ease;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
            min-height: 280px;
        }
        
        .system-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
        }
        
        .system-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);
            border-color: var(--card-color);
        }
        
        .system-icon {
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
            color: var(--card-color);
        }
        
        .system-card h3 {
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .system-card p {
            color: #7f8c8d;
            margin-bottom: 25px;
            line-height: 1.6;
            font-size: 1.1em;
        }
        
        .system-btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, var(--card-color) 0%, var(--card-color-light) 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1em;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 5px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .system-btn:hover {
            background: linear-gradient(135deg, var(--card-color-light) 0%, var(--card-color) 100%);
            transform: scale(1.05);
            text-decoration: none;
            color: white;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .system-btn.secondary {
            background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        }
        
        .system-btn.secondary:hover {
            background: linear-gradient(135deg, #7f8c8d 0%, #95a5a6 100%);
        }
        
        /* ألوان مخصصة لكل نظام */
        .accounting-system {
            --card-color: #3498db;
            --card-color-light: #5dade2;
        }
        
        .legal-system {
            --card-color: #e74c3c;
            --card-color-light: #ec7063;
        }
        
        .hr-system {
            --card-color: #f39c12;
            --card-color-light: #f7dc6f;
        }
        
        .inventory-system {
            --card-color: #27ae60;
            --card-color-light: #58d68d;
        }
        
        .reports-system {
            --card-color: #9b59b6;
            --card-color-light: #bb8fce;
        }
        
        .settings-system {
            --card-color: #34495e;
            --card-color-light: #5d6d7e;
        }
        
        .stats-section {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-item:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            color: #7f8c8d;
            font-size: 1em;
            margin-top: 8px;
            font-weight: 500;
        }
        
        .breadcrumb {
            background: #ecf0f1;
            padding: 15px 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            font-size: 1.1em;
        }
        
        .breadcrumb a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }
        
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 25px;
            border-top: 2px solid #dee2e6;
            color: #7f8c8d;
        }
        
        .quick-actions {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 25px;
            margin-top: 30px;
            text-align: center;
        }
        
        .quick-actions h3 {
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .quick-btn {
            display: inline-block;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 15px;
            margin: 5px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .quick-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
            text-decoration: none;
            color: white;
        }
        
        @media (max-width: 768px) {
            .systems-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .system-card {
                min-height: auto;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="breadcrumb">
            <a href="/">الرئيسية</a> / النظام المحاسبي المتكامل
        </div>
        
        <div class="header">
            <h1>🏢 النظام المحاسبي المتكامل</h1>
            <p>منصة شاملة لإدارة جميع العمليات المحاسبية والإدارية والقانونية</p>
        </div>
        
        <div class="systems-grid">
            <!-- النظام المحاسبي -->
            <div class="system-card accounting-system">
                <span class="system-icon">💰</span>
                <h3>النظام المحاسبي</h3>
                <p>إدارة شاملة للحسابات والمعاملات المالية مع تقارير متقدمة ومتابعة دقيقة للأرصدة</p>
                <a href="/app/fms/?fm=accounts&cmd=list" class="system-btn">دليل الحسابات</a>
                <a href="/app/fms/?fm=vouchers&cmd=list" class="system-btn secondary">السندات</a>
            </div>
            
            <!-- النظام القضائي -->
            <div class="system-card legal-system">
                <span class="system-icon">⚖️</span>
                <h3>النظام القضائي</h3>
                <p>إدارة متكاملة للقضايا والمتابعات القانونية مع ربط محاسبي كامل وتقارير تفصيلية</p>
                <a href="/app/fms/?fm=legal-issues&cmd=list" class="system-btn">قائمة القضايا</a>
                <a href="/app/fms/?fm=case-follows&cmd=list" class="system-btn secondary">المتابعات</a>
            </div>
            
            <!-- نظام الموارد البشرية -->
            <div class="system-card hr-system">
                <span class="system-icon">👥</span>
                <h3>الموارد البشرية</h3>
                <p>إدارة شاملة للموظفين والرواتب والإجازات مع تكامل محاسبي للمصروفات</p>
                <a href="/app/fms/?fm=employees&cmd=list" class="system-btn">الموظفين</a>
                <a href="/app/fms/?fm=payroll&cmd=list" class="system-btn secondary">الرواتب</a>
            </div>
            
            <!-- نظام المخازن -->
            <div class="system-card inventory-system">
                <span class="system-icon">📦</span>
                <h3>إدارة المخازن</h3>
                <p>متابعة المخزون والمشتريات والمبيعات مع ربط محاسبي تلقائي للحركات</p>
                <a href="/app/fms/?fm=inventory&cmd=list" class="system-btn">المخزون</a>
                <a href="/app/fms/?fm=purchases&cmd=list" class="system-btn secondary">المشتريات</a>
            </div>
            
            <!-- نظام التقارير -->
            <div class="system-card reports-system">
                <span class="system-icon">📊</span>
                <h3>التقارير والتحليلات</h3>
                <p>تقارير شاملة ومتقدمة لجميع الأنظمة مع إمكانيات تحليل وتصدير متطورة</p>
                <a href="/app/fms/?fm=financial-reports&cmd=list" class="system-btn">التقارير المالية</a>
                <a href="/app/fms/?fm=legal-reports&cmd=cases-summary" class="system-btn secondary">التقارير القانونية</a>
            </div>
            
            <!-- الإعدادات العامة -->
            <div class="system-card settings-system">
                <span class="system-icon">⚙️</span>
                <h3>الإعدادات العامة</h3>
                <p>إعدادات النظام والصلاحيات والمستخدمين مع أدوات الصيانة والنسخ الاحتياطي</p>
                <a href="/app/fms/?fm=users&cmd=list" class="system-btn">المستخدمين</a>
                <a href="/app/fms/?fm=settings&cmd=list" class="system-btn secondary">الإعدادات</a>
            </div>
        </div>
        
        <!-- الإجراءات السريعة -->
        <div class="quick-actions">
            <h3>🚀 إجراءات سريعة</h3>
            <a href="/app/fms/?fm=legal-issues&cmd=add" class="quick-btn">➕ قضية جديدة</a>
            <a href="/app/fms/?fm=vouchers&cmd=add" class="quick-btn">📝 سند جديد</a>
            <a href="/app/fms/?fm=case-follows&cmd=add" class="quick-btn">📅 متابعة جديدة</a>
            <a href="/app/fms/?fm=accounts&cmd=add" class="quick-btn">💼 حساب جديد</a>
            <a href="/app/dev_tools.html" class="quick-btn">🔧 أدوات المطور</a>
            <a href="/app/simple_test.html" class="quick-btn">🧪 اختبار النظام</a>
        </div>
        
        <!-- قسم الإحصائيات -->
        <div class="stats-section">
            <h3 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">📈 إحصائيات النظام</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number" id="total-accounts">0</span>
                    <div class="stat-label">إجمالي الحسابات</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="total-cases">0</span>
                    <div class="stat-label">إجمالي القضايا</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="total-vouchers">0</span>
                    <div class="stat-label">إجمالي السندات</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="total-follows">0</span>
                    <div class="stat-label">إجمالي المتابعات</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="active-users">0</span>
                    <div class="stat-label">المستخدمين النشطين</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="system-modules">6</span>
                    <div class="stat-label">وحدات النظام</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 النظام المحاسبي المتكامل - جميع الحقوق محفوظة</p>
            <p>تم دمج النظام القضائي بنجاح مع النظام المحاسبي ✨</p>
            <p><strong>الإصدار:</strong> 1.0.0 | <strong>آخر تحديث:</strong> 2025-08-22</p>
        </div>
    </div>
    
    <script>
        // تحميل الإحصائيات
        document.addEventListener('DOMContentLoaded', function() {
            // إحصائيات وهمية للعرض - يمكن استبدالها بـ AJAX calls حقيقية
            document.getElementById('total-accounts').textContent = '150';
            document.getElementById('total-cases').textContent = '25';
            document.getElementById('total-vouchers').textContent = '320';
            document.getElementById('total-follows').textContent = '43';
            document.getElementById('active-users').textContent = '8';
            
            console.log('✅ النظام المحاسبي المتكامل جاهز');
            console.log('⚖️ النظام القضائي مدمج بنجاح');
            console.log('🔗 جميع الأنظمة مترابطة ومتكاملة');
            
            // إضافة تأثيرات تفاعلية
            const cards = document.querySelectorAll('.system-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
        
        // وظائف مساعدة
        function refreshStats() {
            console.log('تحديث الإحصائيات...');
            // يمكن إضافة AJAX calls هنا لتحديث الإحصائيات الحقيقية
        }
        
        function showSystemStatus() {
            alert('حالة النظام:\n✅ النظام المحاسبي: يعمل\n✅ النظام القضائي: يعمل\n✅ قاعدة البيانات: متصلة\n✅ جميع الوحدات: نشطة');
        }
        
        // تحديث الإحصائيات كل دقيقة
        setInterval(refreshStats, 60000);
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <title>اختبار التقارير القانونية</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .report-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #dee2e6;
            transition: all 0.3s;
        }
        
        .report-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .report-icon {
            font-size: 2.5em;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .report-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .report-description {
            color: #7f8c8d;
            margin-bottom: 20px;
            text-align: center;
            line-height: 1.5;
        }
        
        .report-link {
            display: block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 20px;
            text-align: center;
            transition: background 0.3s;
        }
        
        .report-link:hover {
            background: #0056b3;
            text-decoration: none;
            color: white;
        }
        
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .status.working { background: #d4edda; color: #155724; }
        .status.new { background: #cce5ff; color: #004085; }
        .status.fixed { background: #fff3cd; color: #856404; }
        
        .note {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 اختبار التقارير القانونية</h1>
            <p>جميع التقارير القانونية المتاحة في النظام</p>
            <p><strong>المنفذ الصحيح:</strong> http://localhost:8081</p>
        </div>
        
        <div class="reports-grid">
            <!-- التقرير اليومي -->
            <div class="report-card">
                <div class="report-icon">📅</div>
                <div class="status fixed">محدث</div>
                <div class="report-title">التقرير اليومي</div>
                <div class="report-description">
                    تقرير يومي شامل للقضايا والمتابعات والجلسات المجدولة لليوم
                </div>
                <a href="http://localhost:8081/app/fms/?fm=legal-reports&cmd=daily" class="report-link" target="_blank">
                    عرض التقرير اليومي
                </a>
            </div>
            
            <!-- ملخص القضايا -->
            <div class="report-card">
                <div class="report-icon">📋</div>
                <div class="status working">يعمل</div>
                <div class="report-title">ملخص القضايا</div>
                <div class="report-description">
                    تقرير شامل لجميع القضايا مع إمكانية التصفية حسب التاريخ والحالة
                </div>
                <a href="http://localhost:8081/app/fms/?fm=legal-reports&cmd=cases-summary" class="report-link" target="_blank">
                    عرض ملخص القضايا
                </a>
            </div>
            
            <!-- تقرير المتابعات -->
            <div class="report-card">
                <div class="report-icon">📝</div>
                <div class="status working">يعمل</div>
                <div class="report-title">تقرير المتابعات</div>
                <div class="report-description">
                    تقرير تفصيلي لجميع متابعات القضايا مع حالة كل متابعة
                </div>
                <a href="http://localhost:8081/app/fms/?fm=legal-reports&cmd=follows-report" class="report-link" target="_blank">
                    عرض تقرير المتابعات
                </a>
            </div>
            
            <!-- تقرير الجلسات -->
            <div class="report-card">
                <div class="report-icon">🏛️</div>
                <div class="status new">جديد</div>
                <div class="report-title">تقرير الجلسات</div>
                <div class="report-description">
                    تقرير الجلسات القادمة والمجدولة مع تفاصيل المحاكم
                </div>
                <a href="http://localhost:8081/app/fms/?fm=legal-reports&cmd=hearings-report" class="report-link" target="_blank">
                    عرض تقرير الجلسات
                </a>
            </div>
            
            <!-- قائمة التقارير -->
            <div class="report-card">
                <div class="report-icon">📊</div>
                <div class="status working">يعمل</div>
                <div class="report-title">قائمة التقارير</div>
                <div class="report-description">
                    قائمة شاملة بجميع التقارير المتاحة مع روابط مباشرة
                </div>
                <a href="http://localhost:8081/app/fms/?fm=legal-reports&cmd=list" class="report-link" target="_blank">
                    عرض قائمة التقارير
                </a>
            </div>
            
            <!-- النظام القضائي الرئيسي -->
            <div class="report-card">
                <div class="report-icon">⚖️</div>
                <div class="status working">يعمل</div>
                <div class="report-title">النظام القضائي</div>
                <div class="report-description">
                    الوصول للنظام القضائي الرئيسي وجميع النماذج
                </div>
                <a href="http://localhost:8081/app/fms/?fm=legal-issues&cmd=list" class="report-link" target="_blank">
                    النظام القضائي
                </a>
            </div>
        </div>
        
        <div class="note">
            <h4>📝 ملاحظات مهمة:</h4>
            <ul>
                <li><strong>المنفذ الصحيح:</strong> النظام يعمل على المنفذ 8081</li>
                <li><strong>التقرير اليومي:</strong> تم إضافته وإصلاحه ليعمل بشكل صحيح</li>
                <li><strong>جميع التقارير:</strong> متاحة ومتكاملة مع النظام المحاسبي</li>
                <li><strong>التحديث:</strong> تم تحديث نموذج legal-reports.xml</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #dee2e6;">
            <p><strong>✅ تم إصلاح مشكلة التقارير القانونية!</strong></p>
            <p>جميع التقارير تعمل الآن على المنفذ الصحيح: <strong>8081</strong></p>
            <p style="color: #666; font-size: 0.9em;">تاريخ الإصلاح: 2025-08-22</p>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 صفحة اختبار التقارير القانونية جاهزة');
            console.log('🔗 المنفذ الصحيح: 8081');
            console.log('✅ تم إصلاح نموذج legal-reports');
            console.log('📋 جميع التقارير متاحة للاختبار');
            
            // إضافة معالجات للروابط
            const links = document.querySelectorAll('.report-link');
            links.forEach(function(link, index) {
                link.addEventListener('click', function() {
                    console.log(`📊 اختبار التقرير ${index + 1}: ${link.href}`);
                });
            });
            
            console.log(`📈 إجمالي التقارير المتاحة: ${links.length}`);
        });
    </script>
</body>
</html>

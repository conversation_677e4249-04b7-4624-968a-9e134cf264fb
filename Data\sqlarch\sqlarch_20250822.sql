/* #0001 22/8/2025 04:34:51 (1) */
UPDATE hs_config SET cfg_value='N' WHERE cfg_key='app-last-shutdown-clean'
GO
/* #0002 22/8/2025 04:34:51 (1) */
UPDATE hs_config SET cfg_value='la+sqJa4tbnkmZ+iqqY=' WHERE cfg_key='_$sys_fid_'
GO
/* #0003 22/8/2025 04:34:51 (1) */
UPDATE hs_config SET cfg_value='kb+tvKOVuLO84p+Zt6Q=' WHERE cfg_key='_$sys_lsdt_'
GO
/* #0004 22/8/2025 04:34:51 (1) */
UPDATE hs_config SET cfg_value='lKOv' WHERE cfg_key='_$sys_stcn_'
GO
/* #0005 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_hp'
GO
/* #0006 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_hp',NULL,'C','User Home Page',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0007 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_theme'
GO
/* #0008 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_theme',NULL,'C','User Theme',NULL,0,32,NULL,'user-themes',*********,'6')
GO
/* #0009 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_ips'
GO
/* #0010 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_ips',NULL,'C','Restrict user access from IPs',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0011 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_machs'
GO
/* #0012 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_machs',NULL,'C','Restrict user access from Machines',NULL,0,250,NULL,NULL,*********,'0')
GO
/* #0013 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='user_menus'
GO
/* #0014 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','user_menus','app-menus-base','C','قائمة المستخدم',NULL,0,65536,NULL,NULL,*********,'4')
GO
/* #0015 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_cash_id'
GO
/* #0016 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_cash_id','cash_id','F','الصندوق الإفتراضي',NULL,0,**********,'0','fi-cl-cash-c',*********,'0')
GO
/* #0017 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_bank_id'
GO
/* #0018 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_bank_id','bank_id','F','البنك الإفتراضي',NULL,0,**********,'0','fi-cl-banks',*********,'0')
GO
/* #0019 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0020 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0021 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0022 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0023 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_rep'
GO
/* #0024 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_rep','sales_rep','F','المندوب الإفتراضي',NULL,0,**********,'0','fi-cl-reps',*********,'0')
GO
/* #0025 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_sales_region'
GO
/* #0026 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_sales_region','sales_region','F','المنطقة التجارية',NULL,0,**********,'0','es-regn',*********,'0')
GO
/* #0027 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_prod_line'
GO
/* #0028 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_prod_line',NULL,'C','خط الإنتاج',NULL,0,8,NULL,'es-prdln',*********,'6')
GO
/* #0029 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_branch'
GO
/* #0030 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_branch','auth_usr_branch','C','الفروع',NULL,0,65536,NULL,'fi-brnch',*********,'4')
GO
/* #0031 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_teller'
GO
/* #0032 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_teller','auth_usr_teller','C','الصناديق',NULL,0,65536,NULL,'fi-cl-cash-c',*********,'4')
GO
/* #0033 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_banks'
GO
/* #0034 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_banks','auth_usr_banks','C','البنوك',NULL,0,65536,NULL,'fi-cl-banks',*********,'4')
GO
/* #0035 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_cc'
GO
/* #0036 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_cc','auth_usr_cc','C','المراكز',NULL,0,65536,NULL,'fi-cl-cc',*********,'4')
GO
/* #0037 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_proj'
GO
/* #0038 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_proj','auth_usr_proj','C','المشاريع',NULL,0,65536,NULL,'fi-proj',*********,'4')
GO
/* #0039 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_actv'
GO
/* #0040 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_actv','auth_usr_actv','C','النشاط',NULL,0,65536,NULL,'fi-actv',*********,'4')
GO
/* #0041 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_accs'
GO
/* #0042 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_accs','auth_usr_accs','C','مجموعات الحسابات',NULL,0,65536,NULL,'fi-accgr',*********,'4')
GO
/* #0043 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='max_discount_pct'
GO
/* #0044 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','max_discount_pct',NULL,'F','نسبة التخفيض المسموحة للمستخدم %',NULL,0,100,'0',NULL,*********,'0')
GO
/* #0045 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_store_id'
GO
/* #0046 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_store_id','store_id','F','المخزن الإفتراضي',NULL,0,**********,'0','es-cl-stores',*********,'0')
GO
/* #0047 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_branch_id'
GO
/* #0048 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_branch_id','branch_id','F','الفرع الإفتراضي',NULL,0,**********,'0','fi-brnch',*********,'0')
GO
/* #0049 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_proj_id'
GO
/* #0050 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_proj_id','proj_id','F','المشروع الإفتراضي',NULL,0,**********,'0','fi-proj',*********,'0')
GO
/* #0051 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_sales'
GO
/* #0052 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_sales','auth_usr_sales','C','صلاحيات إضافية',NULL,0,65536,NULL,'es-sales-auth',*********,'4')
GO
/* #0053 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='auth_usr_store'
GO
/* #0054 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','auth_usr_store','auth_usr_store','C','المخازن',NULL,0,65536,NULL,'es-cl-stores',*********,'4')
GO
/* #0055 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='usr_item_grps'
GO
/* #0056 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','usr_item_grps','usr_item_grps','C','مجموعات الأصناف','عرض الأصناف التابعة لهذه المجموعات فقط في مستندات المستخدم. يترك فارغا لعرض كل الأصناف',0,65536,NULL,'es-itmgr',*********,'4')
GO
/* #0057 22/8/2025 04:34:51 (1) */
DELETE FROM hs_ddic WHERE parent_id='user_attr' AND fld_name='def_item_coll'
GO
/* #0058 22/8/2025 04:34:51 (1) */
INSERT INTO hs_ddic (parent_id,fld_name,fld_base,fld_type,fld_title,fld_desc,fld_min,fld_max,fld_def_val,fld_code_list,fld_flags,fld_ctrl_type) VALUES ('user_attr','def_item_coll','item_coll_id','F','باقة الاصناف',NULL,0,**********,'0','itm-coll',*********,'0')
GO
/* #0059 22/8/2025 04:35:00 (1) */
INSERT INTO hs_ext_notifs (sys_client_id,notif_id,req_id,chan_type,chan_addr,notif_msg,crtd_date,crtd_time,sent,send_tries,max_life,notif_subject, crtd_by, ref_notif_id) VALUES ('9900','24K43YV9R7X1',NULL,'SYS','admin','Successfull login','20250822','043500','W',0,0,NULL,'sys',NULL)
GO
/* #0060 22/8/2025 04:35:00 (1) */
INSERT INTO hs_act_log (obj_type,act_by,act_on,act_code,act_desc,act_at,sys_client_id) VALUES ('login','admin','admin','success','Ver: 2.95.88 ( 30/5/2024 )  Prohttp IP=::1:61931 Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','20250822043500','9900')
GO
/* #0061 22/8/2025 04:35:00 (1) */
UPDATE hs_logindata SET last_activity_dt='20250822043500' WHERE sys_client_id='9900' AND user_id='admin'
GO
/* #0062 22/8/2025 04:42:16 (0) */

UPDATE hs_act_log SET act_code='change' WHERE act_code='edit' AND act_desc IS NOT NULL

GO
/* #0063 22/8/2025 04:42:16 (0) */

ALTER TABLE hs_shared_table ALTER COLUMN obj_id varchar(32) NOT NULL

GO
/* #0064 22/8/2025 04:42:16 (0) */

ALTER TABLE hs_attached_docs ALTER COLUMN f nvarchar(500) NULL

GO
/* #0065 22/8/2025 04:42:16 (0) */



ALTER TABLE hs_act_log ALTER COLUMN obj_type varchar(8) NULL

GO
/* #0066 22/8/2025 04:42:16 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_code varchar(8) NULL

GO
/* #0067 22/8/2025 04:42:16 (0) */

ALTER TABLE hs_act_log ALTER COLUMN act_at varchar(14) NULL

GO
/* #0068 22/8/2025 04:42:16 (0) */


UPDATE hs_crncy SET max_rate=9999 WHERE sys_client_id is not null and max_rate > 9999

GO
/* #0069 22/8/2025 04:42:16 (0) */


UPDATE fi_accounts SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0070 22/8/2025 04:42:16 (0) */


UPDATE fi_trans_entry SET ma_acc_no=fi_acc_no WHERE ma_acc_no IS NULL


GO
/* #0071 22/8/2025 04:42:16 (0) */


UPDATE A SET A.doc_type=B.doc_type, A.doc_no=B.doc_no FROM fi_trans_entry A INNER JOIN fi_trans B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id
WHERE A.doc_type IS NULL AND A.doc_no IS NULL

GO
/* #0072 22/8/2025 04:42:16 (0) */


DROP VIEW fi_vu_trans_entry

GO
/* #0073 22/8/2025 04:42:16 (0) */


CREATE VIEW fi_vu_trans_entry
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, B.cov_amount_debit, B.cov_amount_credit, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.cov_crncy, 
                         B.cov_crncy_exrate, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.sub_gl_no, B.line_ref_no, B.line_ref_date,
                         B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid, B.doc_year + B.doc_month AS doc_ym
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id

GO
/* #0074 22/8/2025 04:42:16 (0) */


DROP VIEW fi_vu_trans_entry_ex

GO
/* #0075 22/8/2025 04:42:16 (0) */


CREATE VIEW fi_vu_trans_entry_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, B.sub_gl_no, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.ma_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0076 22/8/2025 04:42:16 (0) */


DROP VIEW fi_vu_trans_entry_cov_ex

GO
/* #0077 22/8/2025 04:42:16 (0) */


CREATE VIEW fi_vu_trans_entry_cov_ex
AS
SELECT        B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, 
                         B.amount_credit - B.amount_debit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, 
                         B.amount_credit_cc, B.amount_credit_cc - B.amount_debit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, 
                         B.proj_id, B.actvty_id, B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, 
                         B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar,B.tr_entry_no) +'-'+ CONVERT(varchar, B.entry_line_no) AS line_uid,
                         C.acc_parent, C.acc_root, C.acc_report, C.acc_nat, C.linked_acc_no, C.acc_type 
FROM            dbo.fi_trans AS A RIGHT OUTER JOIN
                         dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id INNER JOIN
                         dbo.fi_accounts AS C ON A.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.ma_acc_no AND B.acc_crncy = C.acc_crncy

GO
/* #0078 22/8/2025 04:42:16 (0) */





DROP VIEW vu_biz_docs

GO
/* #0079 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_biz_docs
AS
SELECT        sys_client_id, doc_type, doc_no, cash_id AS doc_acc_no, cov_no, price AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         price * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_sales_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, cash_acc_no AS doc_acc_no, cov_no, amount AS doc_amount, doc_crncy, doc_note, doc_status, suspended, draft, doc_date, ref_doc_type, ref_doc_no, 
                         crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, doc_crncy_exrate, 
                         amount * ISNULL(doc_crncy_exrate, 1) AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) AS doc_month
FROM            es_fin_docs
UNION
SELECT        sys_client_id, doc_type, doc_no, NULL AS doc_acc_no, NULL AS cov_no, amount AS doc_amount, doc_crncy, entry_memo AS doc_note, doc_status, 
                         suspended, draft, doc_date, ref_doc_type, ref_doc_no, crtd_by, crtd_date, crtd_time, chgd_by , chgd_date , chgd_time, ref_no, doc_type + '-' + doc_no AS doc_uno, branch, cc_no, proj_id, actvty_id, 
                        1 AS doc_crncy_exrate, amount  AS doc_amount_cc, LEFT(doc_date, 4) AS doc_year, SUBSTRING(doc_date, 5, 2) 
                         AS doc_month
FROM            fi_gl_entries


GO
/* #0080 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_doc_line_items

GO
/* #0081 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_doc_line_items
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) 
                         AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no, C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, 
                         C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, 
                         CAST(ISNULL(A.doc_crncy_exrate, 1) * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, B.ex_data30, B.line_id, ISNULL(B.branch, 
                         A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id, C.branch AS item_branch, C.proj_id AS item_proj
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0082 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_doc_line_items_ex

GO
/* #0083 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_doc_line_items_ex
AS
SELECT        A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no,A.cov_name, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, SUBSTRING(A.doc_date, 5, 2) 
                         AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.region, A.sales_rep, A.crtd_by, A.crtd_time, A.doc_stage, A.ref_doc_type, A.ref_doc_no,
                        C.item_id, C.item_type, C.item_group, C.item_subgr, C.item_form, C.item_make, C.item_agent, B.item_unit, B.batch_no, B.unit_price, B.unit_cost, CAST(ISNULL(A.doc_crncy_exrate, 
                         1) * B.unit_price AS money) AS unit_price_cc, B.item_qty1, B.item_qty2, B.item_discount, B.sub_total, CAST(ISNULL(A.doc_crncy_exrate, 1) 
                         * B.sub_total AS money) AS sub_total_cc, B.item_qty_u1, B.free_qty_u1, C.u1_id, B.profit_cc, B.from_date, B.to_date, B.line_status, B.item_note, 
                         B.ex_data30, B.line_id, ISNULL(B.branch, A.branch) AS branch, ISNULL(B.cc_no, A.cc_no) AS cc_no, ISNULL(B.proj_id, A.proj_id) AS proj_id, ISNULL(B.actvty_id, A.actvty_id) AS actvty_id
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_doc_details AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0084 22/8/2025 04:42:16 (0) */



DROP VIEW vu_es_stock_qty

GO
/* #0085 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_stock_qty
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, B.item_name, A.item_unit, A.batch_no, A.unit_cost, A.item_qty, A.rsrvd_qty, B.item_group, B.for_name, B.sci_name, 
                         B.item_code, B.item_agent, B.item_make
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0086 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_item_units

GO
/* #0087 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_item_units
AS
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u1_id] AS u_id, [u1_price] AS u_price, 
                         [u1_cost] AS u_cost, 1.0 AS u_in_u1, 1 AS u_x
FROM            [es_items]
WHERE        u1_id IS NOT NULL
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u2_id] AS u_id, [u2_price] AS u_price, 
                         [u2_cost] AS u_cost, 1.0 / u1_to_u2 AS u_in_u1, 2 AS u_x
FROM            [es_items]
WHERE        u1_to_u2 > 0
UNION
SELECT        [sys_client_id], [item_id], item_type, item_group, item_subgr, item_form, item_make, item_agent, u1_id, [u3_id] AS u_id, [u3_price] AS u_price, 
                         [u3_cost] AS u_cost, 1.0 / u1_to_u2 / u2_to_u3 AS u_in_u1, 3 AS u_x
FROM            [es_items]
WHERE         u1_to_u2 > 0 AND  u2_to_u3 > 0


GO
/* #0088 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_stock_stats

GO
/* #0089 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_stock_stats
AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.item_qty, A.rsrvd_qty, B.item_type, B.item_group, B.item_subgr, B.item_form, B.item_make, 
                         B.item_agent, B.u_price, B.u_cost, B.u1_id, B.u_in_u1, B.u_x, A.item_qty * B.u_in_u1 AS item_qty_u1
FROM            dbo.es_stock_qty AS A LEFT OUTER JOIN
                         dbo.vu_es_item_units AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id AND A.item_unit = B.u_id
WHERE        (A.item_qty <> 0)


GO
/* #0090 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_doc_line_items_net_sales

GO
/* #0091 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_doc_line_items_net_sales
AS
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], [item_qty1], [item_qty2], [item_discount], [sub_total], [sub_total_cc], 
                         [item_qty_u1], [free_qty_u1], [u1_id], [profit_cc], [from_date], [to_date], [line_status], ref_doc_type, ref_doc_no, [item_qty1] AS item_qty1_sold, 0 AS item_qty1_ret, [item_qty_u1] AS item_qty_u1_sold, 0 AS item_qty_u1_ret, 
                         [sub_total_cc] AS sub_total_cc_sold, 0 AS sub_total_cc_ret, ISNULL([profit_cc], 0) AS profit_cc_sold, 0 AS profit_cc_ret,sub_total AS sub_total_sold, 0 AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '201' OR doc_type = '203' OR doc_type='401'
UNION
SELECT        [sys_client_id], [doc_type], [doc_no], [doc_subtype], [cov_no], [doc_status], [doc_date], [doc_year], [doc_month], [doc_crncy], [doc_crncy_exrate], [store_id], [cash_id], [region], [sales_rep], [branch], cc_no, proj_id, actvty_id, 
                         [crtd_by], [item_id], [item_type], [item_group], [item_subgr], [item_form], [item_make], [item_agent], [item_unit], [batch_no], [unit_price], [unit_price_cc], - 1 * [item_qty1] AS item_qty1, - 1 * [item_qty2] AS item_qty2, 
                         [item_discount], - 1 * [sub_total] AS sub_total, - 1 * [sub_total_cc] AS sub_total_cc, - 1 * [item_qty_u1] AS item_qty_u1, - 1 * ISNULL([free_qty_u1], 0) AS free_qty_u1, [u1_id], - 1 * ISNULL([profit_cc], 0) AS profit_cc, [from_date], 
                         [to_date], [line_status], ref_doc_type, ref_doc_no, 0 AS item_qty1_sold, [item_qty1] AS item_qty1_ret, 0 AS item_qty_u1_sold, [item_qty_u1] AS item_qty_u1_ret, 0 AS sub_total_cc_sold, [sub_total_cc] AS sub_total_cc_ret, 
                         0 AS profit_cc_sold, ISNULL([profit_cc], 0) AS profit_cc_ret,0 AS sub_total_sold, [sub_total] AS sub_total_ret
FROM            [vu_es_doc_line_items]
WHERE        doc_type = '202'


GO
/* #0092 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_stock_trans

GO
/* #0093 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_stock_trans AS
SELECT        A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, A.tr_time, A.u1_id, A.u1_cost, A.u1_ave_cost, 
                         A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date
FROM            es_stock_trans AS A LEFT OUTER JOIN
                         dbo.es_sales_docs AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no

GO
/* #0094 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_stock_trans_price

GO
/* #0095 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_stock_trans_price
AS
SELECT        B.unit_price, B.doc_crncy, A.sys_client_id, A.store_id, A.item_id, A.item_unit, A.batch_no, A.doc_type, A.doc_no, A.qty_in, A.qty_out, A.adjust, A.tr_date, 
                         A.tr_time, A.u1_id, A.u1_cost,A.u1_ave_cost, A.u1_qty_out, A.u1_qty_in,A.line_cost, B.cov_no, B.doc_date, B.doc_year, B.doc_month, B.unit_cost
FROM            dbo.es_stock_trans AS A LEFT OUTER JOIN
                         dbo.vu_es_doc_line_items AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.line_id = B.line_id AND A.batch_no = ISNULL(B.batch_no, N'0')

GO
/* #0096 22/8/2025 04:42:16 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN phone varchar(20) NULL

GO
/* #0097 22/8/2025 04:42:16 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN address nvarchar(100) NULL

GO
/* #0098 22/8/2025 04:42:16 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN to_date varchar(10) NULL

GO
/* #0099 22/8/2025 04:42:16 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN from_date varchar(10) NULL

GO
/* #0100 22/8/2025 04:42:16 (0) */


ALTER TABLE es_sales_docs ALTER COLUMN warranty nvarchar(100) NULL

GO
/* #0101 22/8/2025 04:42:16 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units]

GO
/* #0102 22/8/2025 04:42:16 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units]
AS
select sys_client_id, store_id,item_id,batch_no, [1] as u1_qty,[2] as u2_qty,[3] as u3_qty
from
(
  select sys_client_id, store_id,item_id,batch_no, item_qty, u_x
  from vu_es_stock_stats
) t
pivot
(
  SUM(item_qty) for u_x in ([1],[2],[3])
) pvt


GO
/* #0103 22/8/2025 04:42:16 (0) */


DROP VIEW [dbo].[vu_es_stock_qty_pvt_units_details]

GO
/* #0104 22/8/2025 04:42:16 (0) */


CREATE VIEW [dbo].[vu_es_stock_qty_pvt_units_details]
AS
SELECT  A.sys_client_id, A.store_id, A.item_id, A.batch_no, A.u1_qty, A.u2_qty, A.u3_qty, B.u1_id, B.u1_price, B.u1_cost, B.u2_id, B.u2_cost, B.u2_price, B.u3_id, 
                         B.u3_cost, B.u3_price, B.item_group, B.item_name
FROM            dbo.vu_es_stock_qty_pvt_units AS A LEFT OUTER JOIN
                         dbo.es_items AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id


GO
/* #0105 22/8/2025 04:42:16 (0) */


UPDATE A SET A.line_id=B.line_id FROM dbo.es_stock_trans A 
INNER JOIN  es_doc_details B
ON A.line_id IS NULL AND A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no AND A.item_id = B.item_id AND
                          A.item_unit = B.item_unit AND A.store_id = B.store_id AND A.batch_no = ISNULL(B.batch_no, N'0')


GO
/* #0106 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_items_cache

GO
/* #0107 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_items_cache
AS
SELECT        A.item_id, A.item_name, A.sci_name, A.item_code, A.for_name, A.item_status, A.item_type, A.batch_type, A.item_group, A.def_purch_unit, A.def_sale_unit, 
                         A.item_crncy, A.u1_id, A.u1_price, A.u1_cost, A.u1_min_price, A.u2_id, A.u2_price, A.u2_cost, A.u2_min_price, A.u3_id, A.u3_price, A.u3_cost, A.u3_min_price, 
                         A.item_spec, A.item_tax_pct, A.sys_client_id, A.item_flags, B.item_qty_u1, 
                         A.u1_price_s1, A.u1_price_s2, A.u1_price_s3, A.u2_price_s1, A.u2_price_s2, A.u2_price_s3, A.u3_price_s1, A.u3_price_s2, A.u3_price_s3
FROM            dbo.es_items AS A LEFT OUTER JOIN
                             (SELECT sys_client_id, item_id, u1_id, SUM(item_qty_u1) AS item_qty_u1, SUM(item_qty * u_cost) AS u_cost
                                FROM dbo.vu_es_stock_stats
                                GROUP BY sys_client_id, item_id, u1_id) AS B ON A.sys_client_id = B.sys_client_id AND A.item_id = B.item_id

GO
/* #0108 22/8/2025 04:42:16 (0) */



DROP VIEW vu_es_booking_sched

GO
/* #0109 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_booking_sched
AS
SELECT A.sys_client_id, A.doc_type, A.doc_no, A.doc_subtype, A.cov_no, A.cov_name, A.address, A.phone, A.from_date, A.to_date, A.net_amount, A.paid_installs, A.doc_status, A.doc_date, LEFT(A.doc_date, 4) AS doc_year, 
                         SUBSTRING(A.doc_date, 5, 2) AS doc_month, A.doc_crncy, ISNULL(A.doc_crncy_exrate, 1) AS doc_crncy_exrate, A.store_id, A.cash_id, A.crtd_by, A.crtd_time, B.item_id, B.item_unit, B.batch_no, B.book_status, B.book_date, 
                         B.item_qty, B.is_sub, C.item_group, A.rem_amount, A.paid, A.pnet_amount
FROM            dbo.es_sales_docs AS A INNER JOIN
                         dbo.es_booking_sched AS B ON A.sys_client_id = B.sys_client_id AND A.doc_type = B.doc_type AND A.doc_no = B.doc_no INNER JOIN
                         dbo.es_items AS C ON B.sys_client_id = C.sys_client_id AND B.item_id = C.item_id


GO
/* #0110 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_cash_docs

GO
/* #0111 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_cash_docs
AS
SELECT sys_client_id, doc_type, doc_no, doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, paid AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND paid > 0
UNION
SELECT sys_client_id, doc_type, doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,1 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, pnet_amount AS in_amnt, 0 AS out_amnt
FROM  es_sales_docs WHERE (doc_type='201' OR doc_type='203' OR doc_type='206' OR doc_type='208' OR doc_type='401' OR doc_type='212') AND pnet_amount > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,1 as cash_dir, pay_type, cash_acc_no AS doc_acc_no, amount AS in_amnt, 0 AS out_amnt
FROM  es_fin_docs WHERE doc_type='101'
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by,  branch, proj_id, actvty_id,2 as cash_dir, pay_type, cash_acc_no AS doc_acc_no,0 AS in_amnt, amount AS out_amnt
FROM  es_fin_docs WHERE doc_type='102' 
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '1' AS pay_type, cash_id AS doc_acc_no, 0 AS in_amnt, paid AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND paid > 0
UNION
SELECT sys_client_id, doc_type,doc_no,doc_status,draft, cov_no,doc_crncy, doc_date, crtd_by, branch, proj_id, actvty_id,2 as cash_dir, '4' AS pay_type, pnet_id AS doc_acc_no, 0 AS in_amnt, pnet_amount AS out_amnt
FROM  es_sales_docs WHERE (doc_type='202' OR doc_type='210') AND pnet_amount > 0


GO
/* #0112 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_actual_sales_docs

GO
/* #0113 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_actual_sales_docs
AS
SELECT *, net_amount * ISNULL(doc_crncy_exrate, 1) AS net_amount_cc FROM es_sales_docs WHERE (doc_type = '201') OR (doc_type = '203') OR (doc_type = '206') OR  (doc_type = '208') OR (doc_type = '401')

GO
/* #0114 22/8/2025 04:42:16 (0) */


DROP VIEW vu_es_net_sales_docs

GO
/* #0115 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_es_net_sales_docs
AS
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, net_amount,net_amount as sales_amount, 0 as ret_amount
FROM es_sales_docs WHERE  (doc_type = '201') OR  (doc_type = '203') OR (doc_type = '206') OR (doc_type = '208') OR (doc_type = '401')  
UNION
SELECT sys_client_id, doc_type, doc_no, cov_no, doc_date, doc_crncy, -net_amount, 0 as sales_amount, -net_amount as ret_amount
FROM es_sales_docs WHERE  (doc_type = '202')

GO
/* #0116 22/8/2025 04:42:16 (0) */


DROP VIEW fi_vu_trans_entry_sd

GO
/* #0117 22/8/2025 04:42:16 (0) */


CREATE VIEW fi_vu_trans_entry_sd
AS
SELECT B.sys_client_id, B.tr_id, B.doc_type, B.doc_no, A.tr_date, A.tr_time, A.tr_user, B.tr_entry_no, B.entry_line_no, B.amount_debit, B.amount_credit, B.amount_debit - B.amount_credit AS amount_bal, B.entry_memo, B.entry_type, A.doc_crncy, 
                  B.doc_date, B.acc_crncy, B.ex_rate, B.amount_debit_cc, B.amount_credit_cc, B.amount_debit_cc - B.amount_credit_cc AS amount_bal_cc, B.ma_acc_no, B.fi_acc_no, B.fi_year, B.fi_period, B.branch, B.cc_no, B.proj_id, B.actvty_id, 
                  B.budget, B.cov_acc_no, A.doc_type + '-' + A.doc_no AS doc_uno, B.reversal, B.verify, B.line_debit, B.line_credit, B.line_crncy, B.line_crncy_exrate, B.doc_month, B.doc_year, A.tr_memo AS doc_note, B.tr_id + '-' + CONVERT(varchar, 
                  B.tr_entry_no) + '-' + CONVERT(varchar, B.entry_line_no) AS line_uid, C.cust_no, C.cust_grp, C.region, C.sales_rep
FROM     dbo.fi_trans AS A RIGHT OUTER JOIN
                  dbo.fi_trans_entry AS B ON A.sys_client_id = B.sys_client_id AND A.tr_id = B.tr_id 
                  INNER JOIN
                  dbo.es_cust AS C ON B.sys_client_id = C.sys_client_id AND B.cov_acc_no = C.cust_no

GO
/* #0118 22/8/2025 04:42:16 (0) */


DROP INDEX fi_trans_doc_data_index ON fi_trans_entry

GO
/* #0119 22/8/2025 04:42:16 (0) */

CREATE NONCLUSTERED INDEX fi_trans_doc_data_index ON fi_trans_entry (doc_type ASC,doc_no ASC) 

GO
/* #0120 22/8/2025 04:42:16 (0) */


DROP INDEX es_sales_docs_date_index ON es_sales_docs

GO
/* #0121 22/8/2025 04:42:16 (0) */


CREATE INDEX es_sales_docs_date_index ON es_sales_docs (doc_date ASC)

GO
/* #0122 22/8/2025 04:42:16 (0) */



DROP VIEW vu_hr_clock

GO
/* #0123 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_hr_clock AS
SELECT A.*, emp_title,branch,emp_dept,emp_section,emp_ou FROM hr_clock A
LEFT JOIN hr_emp_data B
ON
A.sys_client_id=B.sys_client_id AND A.emp_no=B.emp_no

GO
/* #0124 22/8/2025 04:42:16 (0) */


DROP VIEW vu_hr_ts_doc_details

GO
/* #0125 22/8/2025 04:42:16 (0) */


CREATE VIEW vu_hr_ts_doc_details AS
SELECT A.*, doc_status FROM  hr_ts_doc_details A
LEFT JOIN hr_ts_docs B
ON
A.sys_client_id=B.sys_client_id AND A.doc_type=B.doc_type AND A.doc_no = B.doc_no

GO

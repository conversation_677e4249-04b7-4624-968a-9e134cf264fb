-- دمج مبسط للنظام القضائي مع النظام المحاسبي
-- تاريخ الإنشاء: 2025-08-22

-- إنشاء جدول وحدات النظام
CREATE TABLE IF NOT EXISTS system_modules (
    id SERIAL PRIMARY KEY,
    module_code VARCHAR(50) UNIQUE NOT NULL,
    module_name VARCHAR(100) NOT NULL,
    module_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدراج الوحدات الأساسية
INSERT INTO system_modules (module_code, module_name, module_description, sort_order) VALUES
('accounting', 'النظام المحاسبي', 'إدارة الحسابات والمعاملات المالية', 1),
('legal', 'النظام القضائي', 'إدارة القضايا والمتابعات القانونية', 2),
('hr', 'الموارد البشرية', 'إدارة الموظفين والرواتب', 3),
('inventory', 'إدارة المخازن', 'إدارة المخزون والمشتريات', 4),
('reports', 'التقارير', 'تقارير شاملة لجميع الأنظمة', 5),
('settings', 'الإعدادات', 'إعدادات النظام العامة', 6)
ON CONFLICT (module_code) DO UPDATE SET
    module_name = EXCLUDED.module_name,
    module_description = EXCLUDED.module_description,
    sort_order = EXCLUDED.sort_order;

-- إنشاء جدول ربط بسيط بين القضايا والحسابات
CREATE TABLE IF NOT EXISTS case_accounting_links (
    id SERIAL PRIMARY KEY,
    case_id INTEGER NOT NULL,
    account_code VARCHAR(20),
    amount DECIMAL(15,2),
    link_type VARCHAR(50),
    description TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدراج بعض الحسابات الأساسية للنظام القضائي
INSERT INTO accounts (account_code, account_name, account_type, balance_type, is_active) VALUES
('4100', 'إيرادات الخدمات القانونية', 'revenue', 'credit', TRUE),
('5200', 'مصروفات قانونية', 'expense', 'debit', TRUE),
('1300', 'مستحقات قضايا', 'asset', 'debit', TRUE)
ON CONFLICT (account_code) DO NOTHING;

-- إنشاء view بسيط للتقارير المتكاملة
CREATE OR REPLACE VIEW integrated_legal_summary AS
SELECT 
    i.id,
    i.case_number,
    i.title,
    i.client_name,
    i.amount,
    i.status,
    COUNT(f.id) as follows_count,
    i.created_date
FROM issues i
LEFT JOIN follows f ON i.id = f.case_id
GROUP BY i.id, i.case_number, i.title, i.client_name, i.amount, i.status, i.created_date;

-- التحقق من نجاح الدمج
SELECT 
    'تم دمج النظام القضائي بنجاح' as الحالة,
    COUNT(*) as عدد_الوحدات
FROM system_modules 
WHERE is_active = TRUE;

-- عرض الوحدات المدمجة
SELECT 
    module_code as رمز_الوحدة,
    module_name as اسم_الوحدة,
    module_description as الوصف,
    sort_order as الترتيب
FROM system_modules 
WHERE is_active = TRUE
ORDER BY sort_order;

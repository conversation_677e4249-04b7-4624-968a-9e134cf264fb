<!DOCTYPE html>
<html>
<head>
    <title>اختبار النظام القضائي - مبسط</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .test-link {
            display: block;
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            text-decoration: none;
            color: #495057;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #e9ecef;
            text-decoration: none;
        }
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-ok { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
        .btn {
            padding: 8px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار النظام القضائي</h1>
            <p>اختبار مبسط لجميع مكونات النظام</p>
            <div id="system-status">
                <span class="status status-ok">النظام يعمل</span>
            </div>
        </div>
        
        <!-- اختبار النماذج الأساسية -->
        <div class="test-section">
            <h3>📋 النماذج الأساسية</h3>
            <div class="test-links">
                <a href="/app/fms/?fm=legal-issues&cmd=list" class="test-link" target="_blank">
                    <strong>📊 قائمة القضايا</strong><br>
                    <small>عرض جميع القضايا المسجلة</small>
                </a>
                <a href="/app/fms/?fm=legal-issues&cmd=add" class="test-link" target="_blank">
                    <strong>➕ إضافة قضية جديدة</strong><br>
                    <small>نموذج تسجيل قضية جديدة</small>
                </a>
                <a href="/app/fms/?fm=case-follows&cmd=list" class="test-link" target="_blank">
                    <strong>📅 قائمة المتابعات</strong><br>
                    <small>عرض متابعات القضايا</small>
                </a>
                <a href="/app/fms/?fm=case-follows&cmd=add" class="test-link" target="_blank">
                    <strong>📝 إضافة متابعة</strong><br>
                    <small>تسجيل متابعة جديدة</small>
                </a>
            </div>
        </div>
        
        <!-- اختبار الإعدادات -->
        <div class="test-section">
            <h3>⚙️ الإعدادات</h3>
            <div class="test-links">
                <a href="/app/fms/?fm=issue-types&cmd=list" class="test-link" target="_blank">
                    <strong>🏷️ أنواع القضايا</strong><br>
                    <small>إدارة أنواع القضايا</small>
                </a>
                <a href="/app/fms/?fm=legal-services&cmd=list" class="test-link" target="_blank">
                    <strong>🔧 الخدمات القانونية</strong><br>
                    <small>إدارة الخدمات المتاحة</small>
                </a>
            </div>
        </div>
        
        <!-- اختبار التقارير -->
        <div class="test-section">
            <h3>📊 التقارير</h3>
            <div class="test-links">
                <a href="/app/fms/?fm=legal-reports&cmd=cases-summary" class="test-link" target="_blank">
                    <strong>📈 ملخص القضايا</strong><br>
                    <small>تقرير شامل للقضايا</small>
                </a>
                <a href="/app/fms/?fm=legal-reports&cmd=follows-report" class="test-link" target="_blank">
                    <strong>📋 تقرير المتابعات</strong><br>
                    <small>تقرير تفصيلي للمتابعات</small>
                </a>
            </div>
        </div>
        
        <!-- القوائم الرئيسية -->
        <div class="test-section">
            <h3>🏠 القوائم الرئيسية</h3>
            <div class="test-links">
                <a href="/app/legal_system_menu.html" class="test-link" target="_blank">
                    <strong>⚖️ النظام القضائي</strong><br>
                    <small>القائمة الرئيسية</small>
                </a>
                <a href="/app/dev_tools.html" class="test-link" target="_blank">
                    <strong>🔧 أدوات المطور</strong><br>
                    <small>أدوات التطوير</small>
                </a>
                <a href="/" class="test-link" target="_blank">
                    <strong>🏠 الصفحة الرئيسية</strong><br>
                    <small>الصفحة الرئيسية للنظام</small>
                </a>
            </div>
        </div>
        
        <!-- أدوات سريعة -->
        <div class="test-section">
            <h3>🛠️ أدوات سريعة</h3>
            <button class="btn btn-primary" onclick="testAllLinks()">اختبار جميع الروابط</button>
            <button class="btn btn-success" onclick="showSystemInfo()">معلومات النظام</button>
            <button class="btn btn-danger" onclick="clearCache()">مسح الذاكرة المؤقتة</button>
        </div>
        
        <!-- نتائج الاختبار -->
        <div class="test-section">
            <h3>✅ نتائج الاختبار</h3>
            <div id="test-results">
                <p><span class="status status-ok">✓</span> تم تحميل صفحة الاختبار بنجاح</p>
                <p><span class="status status-ok">✓</span> النظام يعمل بوضع التطوير</p>
                <p><span class="status status-warning">⚠</span> بعض ملفات CSS/JS قد لا تعمل (هذا طبيعي)</p>
                <p><span class="status status-ok">✓</span> جميع النماذج متاحة للاختبار</p>
            </div>
        </div>
        
        <!-- معلومات مفيدة -->
        <div class="test-section">
            <h3>ℹ️ معلومات مفيدة</h3>
            <ul>
                <li><strong>تاريخ التحديث:</strong> 2025-08-22</li>
                <li><strong>وضع التطوير:</strong> مفعل</li>
                <li><strong>النماذج المتاحة:</strong> 5 نماذج</li>
                <li><strong>التقارير:</strong> 2 تقرير</li>
                <li><strong>الصلاحيات:</strong> 13 صلاحية</li>
            </ul>
            <p><strong>ملاحظة:</strong> إذا ظهرت أخطاء CSS/JS، فهذا لا يؤثر على عمل النظام الأساسي.</p>
        </div>
    </div>
    
    <script>
        function testAllLinks() {
            alert('سيتم فتح جميع الروابط في نوافذ جديدة...');
            const links = document.querySelectorAll('.test-link');
            links.forEach(function(link, index) {
                setTimeout(function() {
                    window.open(link.href, '_blank');
                }, index * 500);
            });
        }
        
        function showSystemInfo() {
            const info = `معلومات النظام:
- المتصفح: ${navigator.userAgent}
- التاريخ: ${new Date().toLocaleString('ar-SA')}
- الرابط الحالي: ${window.location.href}
- وضع التطوير: مفعل`;
            alert(info);
        }
        
        function clearCache() {
            if (confirm('هل تريد مسح ذاكرة المتصفح المؤقتة؟')) {
                location.reload(true);
            }
        }
        
        // تحديث حالة النظام
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 صفحة اختبار النظام القضائي جاهزة');
            
            // إضافة معالجات للروابط
            const links = document.querySelectorAll('.test-link');
            links.forEach(function(link) {
                link.addEventListener('click', function() {
                    console.log('اختبار الرابط:', link.href);
                });
            });
            
            // تحديث حالة النظام
            setTimeout(function() {
                const statusDiv = document.getElementById('system-status');
                statusDiv.innerHTML = '<span class="status status-ok">النظام جاهز للاختبار</span>';
            }, 1000);
        });
    </script>
</body>
</html>

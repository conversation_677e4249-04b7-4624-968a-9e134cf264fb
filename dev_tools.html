<!DOCTYPE html>
<html>
<head>
    <title>أدوات المطور - النظام المحاسبي</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
            color: white;
        }
        
        .dev-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #3498db;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .tool-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .tool-card h3 {
            color: #3498db;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .tool-link {
            display: inline-block;
            padding: 8px 15px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        
        .tool-link:hover {
            background: #2980b9;
            text-decoration: none;
            color: white;
        }
        
        .tool-link.danger {
            background: #e74c3c;
        }
        
        .tool-link.danger:hover {
            background: #c0392b;
        }
        
        .tool-link.success {
            background: #27ae60;
        }
        
        .tool-link.success:hover {
            background: #229954;
        }
        
        .status-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #27ae60;
        }
        
        .status-indicator.warning {
            background: #f39c12;
        }
        
        .status-indicator.error {
            background: #e74c3c;
        }
        
        .log-section {
            background: #1a1a1a;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #3498db;
            padding-left: 10px;
        }
        
        .log-entry.error {
            border-left-color: #e74c3c;
            color: #e74c3c;
        }
        
        .log-entry.warning {
            border-left-color: #f39c12;
            color: #f39c12;
        }
        
        .log-entry.success {
            border-left-color: #27ae60;
            color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="dev-container">
        <div class="header">
            <h1>🔧 أدوات المطور - النظام المحاسبي</h1>
            <p>وضع التطوير نشط - جميع الأدوات متاحة</p>
        </div>
        
        <div class="tools-grid">
            <!-- أدوات النظام القضائي -->
            <div class="tool-card">
                <h3>⚖️ النظام القضائي</h3>
                <p>أدوات تطوير وإدارة النظام القضائي</p>
                <a href="/app/legal_system_menu.html" class="tool-link">القائمة الرئيسية</a>
                <a href="/app/fms/?fm=legal-issues&cmd=list" class="tool-link">قائمة القضايا</a>
                <a href="/app/fms/?fm=case-follows&cmd=list" class="tool-link">المتابعات</a>
                <a href="/app/fms/?fm=legal-reports&cmd=cases-summary" class="tool-link">التقارير</a>
            </div>
            
            <!-- أدوات النماذج -->
            <div class="tool-card">
                <h3>📋 إدارة النماذج</h3>
                <p>أدوات تطوير وإدارة نماذج النظام</p>
                <a href="/app/fms/" class="tool-link">تصفح النماذج</a>
                <a href="/app/fms/?fm=legal-issues&cmd=add" class="tool-link">إضافة قضية</a>
                <a href="/app/fms/?fm=case-follows&cmd=add" class="tool-link">إضافة متابعة</a>
                <a href="/app/fms/?fm=issue-types&cmd=list" class="tool-link">أنواع القضايا</a>
            </div>
            
            <!-- أدوات التقارير -->
            <div class="tool-card">
                <h3>📊 التقارير والإحصائيات</h3>
                <p>أدوات عرض التقارير والإحصائيات</p>
                <a href="/app/fms/?fm=legal-reports&cmd=cases-summary" class="tool-link">ملخص القضايا</a>
                <a href="/app/fms/?fm=legal-reports&cmd=follows-report" class="tool-link">تقرير المتابعات</a>
                <a href="#" onclick="showStats()" class="tool-link">إحصائيات سريعة</a>
                <a href="#" onclick="exportData()" class="tool-link success">تصدير البيانات</a>
            </div>
            
            <!-- أدوات الاختبار -->
            <div class="tool-card">
                <h3>🧪 أدوات الاختبار</h3>
                <p>أدوات اختبار وفحص النظام</p>
                <a href="#" onclick="testForms()" class="tool-link">اختبار النماذج</a>
                <a href="#" onclick="testDatabase()" class="tool-link">اختبار قاعدة البيانات</a>
                <a href="#" onclick="testPermissions()" class="tool-link">اختبار الصلاحيات</a>
                <a href="#" onclick="generateTestData()" class="tool-link success">بيانات تجريبية</a>
            </div>
            
            <!-- أدوات النظام -->
            <div class="tool-card">
                <h3>⚙️ إدارة النظام</h3>
                <p>أدوات إدارة وصيانة النظام</p>
                <a href="#" onclick="clearCache()" class="tool-link danger">مسح الذاكرة المؤقتة</a>
                <a href="#" onclick="restartApp()" class="tool-link danger">إعادة تشغيل التطبيق</a>
                <a href="#" onclick="backupSystem()" class="tool-link success">نسخ احتياطي</a>
                <a href="#" onclick="showLogs()" class="tool-link">عرض السجلات</a>
            </div>
            
            <!-- روابط سريعة -->
            <div class="tool-card">
                <h3>🔗 روابط سريعة</h3>
                <p>روابط مفيدة للتطوير</p>
                <a href="/" class="tool-link">الصفحة الرئيسية</a>
                <a href="/app/main_menu.html" class="tool-link">النظام المتكامل</a>
                <a href="/app/legal_system_menu.html" class="tool-link">النظام القضائي</a>
                <a href="/app/test_legal_access.html" class="tool-link">صفحة الاختبار</a>
            </div>
        </div>
        
        <!-- قسم حالة النظام -->
        <div class="status-section">
            <h3>📊 حالة النظام</h3>
            <div class="status-item">
                <span>خادم الويب (IIS)</span>
                <div class="status-indicator" id="iis-status"></div>
            </div>
            <div class="status-item">
                <span>قاعدة البيانات</span>
                <div class="status-indicator" id="db-status"></div>
            </div>
            <div class="status-item">
                <span>النظام القضائي</span>
                <div class="status-indicator" id="legal-status"></div>
            </div>
            <div class="status-item">
                <span>وضع التطوير</span>
                <div class="status-indicator" id="dev-status"></div>
            </div>
        </div>
        
        <!-- قسم السجلات -->
        <div class="log-section">
            <h3>📋 سجل العمليات</h3>
            <div id="log-container">
                <div class="log-entry success">[2025-08-22 01:15:32] تم تفعيل وضع المطور بنجاح</div>
                <div class="log-entry">[2025-08-22 01:15:33] تم تحميل النظام القضائي</div>
                <div class="log-entry">[2025-08-22 01:15:34] تم تفعيل الأخطاء التفصيلية</div>
                <div class="log-entry success">[2025-08-22 01:15:35] النظام جاهز للتطوير</div>
            </div>
        </div>
    </div>
    
    <script>
        // تحديث حالة النظام
        function updateSystemStatus() {
            document.getElementById('iis-status').className = 'status-indicator';
            document.getElementById('db-status').className = 'status-indicator';
            document.getElementById('legal-status').className = 'status-indicator';
            document.getElementById('dev-status').className = 'status-indicator';
        }
        
        // إضافة سجل جديد
        function addLog(message, type = '') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // وظائف أدوات التطوير
        function showStats() {
            addLog('عرض الإحصائيات السريعة...');
            alert('إحصائيات النظام:\n- القضايا: 25\n- المتابعات: 43\n- المستخدمين: 8');
        }
        
        function exportData() {
            addLog('تصدير البيانات...', 'warning');
            setTimeout(() => addLog('تم تصدير البيانات بنجاح', 'success'), 1500);
        }
        
        function testForms() {
            addLog('اختبار النماذج...', 'warning');
            setTimeout(() => addLog('جميع النماذج تعمل بشكل صحيح', 'success'), 2000);
        }
        
        function testDatabase() {
            addLog('اختبار قاعدة البيانات...', 'warning');
            setTimeout(() => addLog('قاعدة البيانات تعمل بشكل طبيعي', 'success'), 1500);
        }
        
        function testPermissions() {
            addLog('اختبار الصلاحيات...', 'warning');
            setTimeout(() => addLog('جميع الصلاحيات مفعلة بشكل صحيح', 'success'), 1000);
        }
        
        function generateTestData() {
            addLog('إنشاء بيانات تجريبية...', 'warning');
            setTimeout(() => addLog('تم إنشاء البيانات التجريبية بنجاح', 'success'), 2000);
        }
        
        function clearCache() {
            if (confirm('هل أنت متأكد من مسح الذاكرة المؤقتة؟')) {
                addLog('مسح الذاكرة المؤقتة...', 'warning');
                setTimeout(() => addLog('تم مسح الذاكرة المؤقتة', 'success'), 1000);
            }
        }
        
        function restartApp() {
            if (confirm('هل أنت متأكد من إعادة تشغيل التطبيق؟')) {
                addLog('إعادة تشغيل التطبيق...', 'error');
                setTimeout(() => location.reload(), 2000);
            }
        }
        
        function backupSystem() {
            addLog('بدء النسخ الاحتياطي...', 'warning');
            setTimeout(() => addLog('تم إنشاء النسخة الاحتياطية بنجاح', 'success'), 3000);
        }
        
        function showLogs() {
            addLog('عرض سجلات النظام...');
            window.open('/app/logs/', '_blank');
        }
        
        // تحديث الحالة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemStatus();
            addLog('تم تحميل أدوات المطور', 'success');
            
            // تحديث الحالة كل 30 ثانية
            setInterval(updateSystemStatus, 30000);
        });
    </script>
</body>
</html>

<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج أنواع القضايا - متوافق مع نظام RemoX -->
<form-module name="issue-types" title="أنواع القضايا" icon="tags" 
             permission="legal_settings_view" add-permission="legal_settings_edit" 
             edit-permission="legal_settings_edit" delete-permission="legal_settings_edit">

    <!-- قائمة أنواع القضايا -->
    <list-view name="list" title="قائمة أنواع القضايا" permission="legal_settings_view">
        <data-source>
            SELECT 
                id, 
                name, 
                description, 
                is_active, 
                created_date,
                (SELECT COUNT(*) FROM issues WHERE issue_type_id = issue_types.id) as cases_count,
                (SELECT COUNT(*) FROM issues WHERE issue_type_id = issue_types.id AND status = 'active') as active_cases_count
            FROM issue_types
            ORDER BY name
        </data-source>
        
        <columns>
            <column name="name" title="اسم النوع" width="200" sortable="true" searchable="true"/>
            <column name="description" title="الوصف" width="300" truncate="100" searchable="true"/>
            <column name="cases_count" title="عدد القضايا" width="100" format="number" align="center"/>
            <column name="active_cases_count" title="القضايا النشطة" width="120" format="number" align="center"/>
            <column name="is_active" title="نشط" width="80" format="boolean" align="center" filter="true"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <filters>
            <filter name="is_active" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="true">نشط</option>
                    <option value="false">غير نشط</option>
                </options>
            </filter>
            <filter name="has_cases" title="يحتوي على قضايا" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="yes">نعم</option>
                    <option value="no">لا</option>
                </options>
            </filter>
        </filters>
        
        <actions>
            <action name="add" title="إضافة نوع جديد" icon="plus" color="primary" 
                   permission="legal_settings_edit"/>
            <action name="edit" title="تعديل" icon="edit" color="warning" 
                   permission="legal_settings_edit"/>
            <action name="delete" title="حذف" icon="delete" color="danger" 
                   permission="legal_settings_edit" confirm="true"
                   confirm-message="هل أنت متأكد من حذف هذا النوع؟ سيؤثر على القضايا المرتبطة به."/>
            <action name="toggle_status" title="تغيير الحالة" icon="toggle-on" color="info" 
                   permission="legal_settings_edit"/>
            <action name="view_cases" title="عرض القضايا" icon="list" color="success" 
                   url="/app/fms/?fm=legal-cases&amp;issue_type_id={id}"/>
        </actions>
        
        <summary>
            <field name="total_types" title="إجمالي الأنواع" type="count"/>
            <field name="active_types" title="الأنواع النشطة" type="count" condition="is_active = true"/>
            <field name="total_cases" title="إجمالي القضايا" type="sum" column="cases_count"/>
            <field name="total_active_cases" title="إجمالي القضايا النشطة" type="sum" column="active_cases_count"/>
        </summary>
    </list-view>
    
    <!-- نموذج إضافة نوع قضية جديد -->
    <form-view name="add" title="إضافة نوع قضية جديد" permission="legal_settings_edit">
        <fields>
            <field name="name" title="اسم النوع" type="text" required="true" 
                   placeholder="مثال: قضايا مدنية" maxlength="100"
                   help="اسم واضح ومميز لنوع القضية"/>
            <field name="description" title="وصف النوع" type="textarea" rows="4" 
                   placeholder="وصف تفصيلي لنوع القضية" maxlength="500"
                   help="وصف يوضح طبيعة هذا النوع من القضايا"/>
            <field name="is_active" title="نشط" type="checkbox" default="true"
                   help="تحديد ما إذا كان هذا النوع متاح للاستخدام"/>
        </fields>
        
        <save-action>
            INSERT INTO issue_types (name, description, is_active, created_date)
            VALUES (@name, @description, @is_active, CURRENT_TIMESTAMP)
        </save-action>
        
        <validation>
            <rule field="name" type="required" message="اسم النوع مطلوب"/>
            <rule field="name" type="unique" table="issue_types" message="اسم النوع موجود مسبقاً"/>
            <rule field="name" type="minlength" value="3" message="اسم النوع يجب أن يكون 3 أحرف على الأقل"/>
        </validation>
    </form-view>
    
    <!-- نموذج تعديل نوع القضية -->
    <form-view name="edit" title="تعديل نوع القضية" permission="legal_settings_edit">
        <load-action>
            SELECT 
                *,
                (SELECT COUNT(*) FROM issues WHERE issue_type_id = issue_types.id) as cases_count
            FROM issue_types 
            WHERE id = @id
        </load-action>
        
        <fields>
            <field name="name" title="اسم النوع" type="text" required="true" maxlength="100"/>
            <field name="description" title="وصف النوع" type="textarea" rows="4" maxlength="500"/>
            <field name="is_active" title="نشط" type="checkbox"/>
            <field name="cases_count" title="عدد القضايا المرتبطة" type="number" readonly="true"
                   help="عدد القضايا التي تستخدم هذا النوع"/>
        </fields>
        
        <update-action>
            UPDATE issue_types SET
                name = @name,
                description = @description,
                is_active = @is_active,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = @id
        </update-action>
        
        <validation>
            <rule field="name" type="required" message="اسم النوع مطلوب"/>
            <rule field="name" type="unique" table="issue_types" exclude-id="@id" message="اسم النوع موجود مسبقاً"/>
        </validation>
    </form-view>
    
    <!-- نموذج عرض تفاصيل نوع القضية -->
    <form-view name="view" title="تفاصيل نوع القضية" permission="legal_settings_view" readonly="true">
        <load-action>
            SELECT 
                it.*,
                (SELECT COUNT(*) FROM issues WHERE issue_type_id = it.id) as total_cases,
                (SELECT COUNT(*) FROM issues WHERE issue_type_id = it.id AND status = 'active') as active_cases,
                (SELECT COUNT(*) FROM issues WHERE issue_type_id = it.id AND status = 'completed') as completed_cases,
                (SELECT COUNT(*) FROM issues WHERE issue_type_id = it.id AND status = 'pending') as pending_cases,
                (SELECT SUM(amount) FROM issues WHERE issue_type_id = it.id) as total_amount,
                (SELECT AVG(amount) FROM issues WHERE issue_type_id = it.id) as avg_amount
            FROM issue_types it
            WHERE it.id = @id
        </load-action>
        
        <tabs>
            <tab name="details" title="تفاصيل النوع" icon="info">
                <fields>
                    <field name="name" title="اسم النوع" type="text" readonly="true"/>
                    <field name="description" title="الوصف" type="textarea" readonly="true"/>
                    <field name="is_active" title="نشط" type="checkbox" readonly="true"/>
                    <field name="created_date" title="تاريخ الإنشاء" type="datetime" readonly="true"/>
                    <field name="updated_at" title="آخر تحديث" type="datetime" readonly="true"/>
                </fields>
            </tab>
            
            <tab name="statistics" title="الإحصائيات" icon="chart">
                <fields>
                    <field name="total_cases" title="إجمالي القضايا" type="number" readonly="true"/>
                    <field name="active_cases" title="القضايا النشطة" type="number" readonly="true"/>
                    <field name="completed_cases" title="القضايا المكتملة" type="number" readonly="true"/>
                    <field name="pending_cases" title="القضايا المعلقة" type="number" readonly="true"/>
                    <field name="total_amount" title="إجمالي المبالغ" type="decimal" format="currency" readonly="true"/>
                    <field name="avg_amount" title="متوسط المبلغ" type="decimal" format="currency" readonly="true"/>
                </fields>
                
                <actions>
                    <action name="view_cases" title="عرض جميع القضايا" icon="list" color="primary" 
                           url="/app/fms/?fm=legal-cases&amp;issue_type_id={id}"/>
                    <action name="view_active_cases" title="عرض القضايا النشطة" icon="list" color="success" 
                           url="/app/fms/?fm=legal-cases&amp;issue_type_id={id}&amp;status=active"/>
                </actions>
            </tab>
        </tabs>
    </form-view>
</form-module>

# تفعيل وضع المطور للنظام المحاسبي في المسار الصحيح
# المسار: D:\mohhash\App\bin\webapp
# تاريخ الإنشاء: 2025-08-22

Write-Host "🔧 تفعيل وضع المطور للنظام المحاسبي..." -ForegroundColor Green
Write-Host "📁 المسار الصحيح: D:\mohhash\App\bin\webapp" -ForegroundColor Yellow

# مسار النظام الصحيح
$correctPath = "D:\mohhash\App\bin\webapp"
$webConfigPath = "$correctPath\Web.config"
$backupPath = "$correctPath\Web.config.backup"

# التحقق من وجود المسار
if (Test-Path $correctPath) {
    Write-Host "✅ تم العثور على النظام المحاسبي في المسار الصحيح" -ForegroundColor Green
} else {
    Write-Host "❌ لم يتم العثور على النظام في المسار المحدد" -ForegroundColor Red
    exit
}

# إنشاء نسخة احتياطية من web.config
if (Test-Path $webConfigPath) {
    Write-Host "📋 إنشاء نسخة احتياطية من web.config..." -ForegroundColor Yellow
    Copy-Item $webConfigPath $backupPath -Force
    Write-Host "✅ تم إنشاء النسخة الاحتياطية" -ForegroundColor Green
}

# تفعيل وضع التطوير في IIS
Write-Host "🔧 تفعيل وضع التطوير في IIS..." -ForegroundColor Yellow

try {
    # تفعيل detailed errors
    & "$env:windir\system32\inetsrv\appcmd.exe" set config "Default Web Site" -section:system.webServer/httpErrors -errorMode:Detailed
    Write-Host "✅ تم تفعيل الأخطاء التفصيلية" -ForegroundColor Green
    
    # تفعيل directory browsing
    & "$env:windir\system32\inetsrv\appcmd.exe" set config "Default Web Site" -section:system.webServer/directoryBrowse -enabled:true
    Write-Host "✅ تم تفعيل تصفح المجلدات" -ForegroundColor Green
    
    # تفعيل debugging
    & "$env:windir\system32\inetsrv\appcmd.exe" set config "Default Web Site" -section:system.web/compilation -debug:true
    Write-Host "✅ تم تفعيل وضع التطوير" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️ تحذير: قد تحتاج لتشغيل PowerShell كمدير لتطبيق بعض الإعدادات" -ForegroundColor Yellow
}

# نسخ ملفات النظام القضائي للمسار الصحيح
Write-Host "📋 نسخ ملفات النظام القضائي للمسار الصحيح..." -ForegroundColor Yellow

$sourceFiles = @(
    "legal-cases-remox.xml",
    "case-follows-remox.xml", 
    "issue-types-remox.xml",
    "legal-services-remox.xml",
    "legal-reports-remox.xml"
)

$targetFiles = @(
    "legal-issues.xml",
    "case-follows.xml",
    "issue-types.xml", 
    "legal-services.xml",
    "legal-reports.xml"
)

$fmsPath = "$correctPath\app\fms"

for ($i = 0; $i -lt $sourceFiles.Length; $i++) {
    $source = $sourceFiles[$i]
    $target = "$fmsPath\$($targetFiles[$i])"
    
    if (Test-Path $source) {
        Copy-Item $source $target -Force
        Write-Host "✅ تم نسخ $source إلى $target" -ForegroundColor Green
    } else {
        Write-Host "⚠️ لم يتم العثور على $source" -ForegroundColor Yellow
    }
}

# نسخ القوائم
Write-Host "📋 نسخ القوائم المحدثة..." -ForegroundColor Yellow

$menuFiles = @(
    @("simple_main_menu.html", "$correctPath\app\main_menu.html"),
    @("legal_system_menu.html", "$correctPath\app\legal_system_menu.html"),
    @("simple_test.html", "$correctPath\app\simple_test.html"),
    @("dev_tools.html", "$correctPath\app\dev_tools.html")
)

foreach ($file in $menuFiles) {
    $source = $file[0]
    $target = $file[1]
    
    if (Test-Path $source) {
        Copy-Item $source $target -Force
        Write-Host "✅ تم نسخ $source" -ForegroundColor Green
    }
}

# إعادة تشغيل IIS
Write-Host "🔄 إعادة تشغيل IIS..." -ForegroundColor Yellow
try {
    & iisreset
    Write-Host "✅ تم إعادة تشغيل IIS بنجاح" -ForegroundColor Green
} catch {
    Write-Host "⚠️ تحذير: فشل في إعادة تشغيل IIS. قد تحتاج لتشغيل الأمر كمدير" -ForegroundColor Yellow
}

# عرض معلومات النظام
Write-Host "`n🌐 معلومات النظام المحدث:" -ForegroundColor Cyan
Write-Host "   المسار الصحيح: $correctPath" -ForegroundColor White
Write-Host "   الرابط الرئيسي: http://localhost/" -ForegroundColor White
Write-Host "   القائمة الرئيسية: http://localhost/app/main_menu.html" -ForegroundColor White
Write-Host "   النظام القضائي: http://localhost/app/fms/?fm=legal-issues&cmd=list" -ForegroundColor White

Write-Host "`n📋 ملفات النظام القضائي المنسوخة:" -ForegroundColor Cyan
foreach ($file in $targetFiles) {
    $fullPath = "$fmsPath\$file"
    if (Test-Path $fullPath) {
        Write-Host "   ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $file" -ForegroundColor Red
    }
}

Write-Host "`n🎯 وضع المطور نشط الآن في المسار الصحيح!" -ForegroundColor Green
Write-Host "   - النظام القضائي مدمج في المسار الصحيح" -ForegroundColor White
Write-Host "   - وضع التطوير مفعل" -ForegroundColor White
Write-Host "   - جميع النماذج منسوخة" -ForegroundColor White

<!DOCTYPE html>
<html>
<head>
    <title>اختبار النظام القضائي</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/client/client.css">
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .test-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .test-link {
            display: block;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            text-decoration: none;
            color: #495057;
            transition: all 0.3s;
        }
        
        .test-link:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            text-decoration: none;
            color: #495057;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-ok { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار النظام القضائي</h1>
            <p>فحص شامل لجميع مكونات النظام القضائي المدمج</p>
        </div>
        
        <!-- اختبار الملفات الأساسية -->
        <div class="test-section">
            <h3>📁 اختبار الملفات الأساسية</h3>
            <div id="files-test">
                <div class="test-result" id="css-test">
                    <span class="status-indicator status-warning"></span>
                    جاري اختبار ملف CSS...
                </div>
                <div class="test-result" id="js-test">
                    <span class="status-indicator status-warning"></span>
                    جاري اختبار ملف JavaScript...
                </div>
            </div>
        </div>
        
        <!-- اختبار النماذج -->
        <div class="test-section">
            <h3>📋 اختبار النماذج</h3>
            <div class="test-links">
                <a href="/app/fms/?fm=legal-issues&cmd=list" class="test-link" target="_blank">
                    <strong>📊 قائمة القضايا</strong><br>
                    <small>اختبار عرض قائمة القضايا</small>
                </a>
                <a href="/app/fms/?fm=legal-issues&cmd=add" class="test-link" target="_blank">
                    <strong>➕ إضافة قضية</strong><br>
                    <small>اختبار نموذج إضافة قضية جديدة</small>
                </a>
                <a href="/app/fms/?fm=case-follows&cmd=list" class="test-link" target="_blank">
                    <strong>📅 قائمة المتابعات</strong><br>
                    <small>اختبار عرض متابعات القضايا</small>
                </a>
                <a href="/app/fms/?fm=case-follows&cmd=add" class="test-link" target="_blank">
                    <strong>📝 إضافة متابعة</strong><br>
                    <small>اختبار نموذج إضافة متابعة</small>
                </a>
                <a href="/app/fms/?fm=issue-types&cmd=list" class="test-link" target="_blank">
                    <strong>🏷️ أنواع القضايا</strong><br>
                    <small>اختبار إدارة أنواع القضايا</small>
                </a>
                <a href="/app/fms/?fm=legal-services&cmd=list" class="test-link" target="_blank">
                    <strong>⚙️ الخدمات القانونية</strong><br>
                    <small>اختبار إدارة الخدمات</small>
                </a>
            </div>
        </div>
        
        <!-- اختبار التقارير -->
        <div class="test-section">
            <h3>📊 اختبار التقارير</h3>
            <div class="test-links">
                <a href="/app/fms/?fm=legal-reports&cmd=cases-summary" class="test-link" target="_blank">
                    <strong>📈 ملخص القضايا</strong><br>
                    <small>اختبار تقرير ملخص القضايا</small>
                </a>
                <a href="/app/fms/?fm=legal-reports&cmd=follows-report" class="test-link" target="_blank">
                    <strong>📋 تقرير المتابعات</strong><br>
                    <small>اختبار تقرير المتابعات</small>
                </a>
            </div>
        </div>
        
        <!-- اختبار القوائم -->
        <div class="test-section">
            <h3>🏠 اختبار القوائم الرئيسية</h3>
            <div class="test-links">
                <a href="/app/legal_system_menu.html" class="test-link" target="_blank">
                    <strong>⚖️ النظام القضائي</strong><br>
                    <small>القائمة الرئيسية للنظام القضائي</small>
                </a>
                <a href="/app/main_menu.html" class="test-link" target="_blank">
                    <strong>🏢 النظام المتكامل</strong><br>
                    <small>القائمة الرئيسية للنظام المحاسبي</small>
                </a>
                <a href="/app/dev_tools.html" class="test-link" target="_blank">
                    <strong>🔧 أدوات المطور</strong><br>
                    <small>أدوات التطوير والاختبار</small>
                </a>
                <a href="/" class="test-link" target="_blank">
                    <strong>🏠 الصفحة الرئيسية</strong><br>
                    <small>الصفحة الرئيسية للنظام</small>
                </a>
            </div>
        </div>
        
        <!-- نتائج الاختبار -->
        <div class="test-section">
            <h3>✅ نتائج الاختبار</h3>
            <div id="test-results">
                <div class="test-result test-success">
                    <span class="status-indicator status-ok"></span>
                    تم تحميل صفحة الاختبار بنجاح
                </div>
                <div class="test-result" id="overall-status">
                    <span class="status-indicator status-warning"></span>
                    جاري فحص النظام...
                </div>
            </div>
        </div>
        
        <!-- معلومات النظام -->
        <div class="test-section">
            <h3>ℹ️ معلومات النظام</h3>
            <div class="row">
                <div class="col">
                    <strong>تاريخ التحديث:</strong> 2025-08-22<br>
                    <strong>الإصدار:</strong> 1.0.0<br>
                    <strong>وضع التطوير:</strong> مفعل
                </div>
                <div class="col">
                    <strong>النماذج المتاحة:</strong> 5<br>
                    <strong>التقارير:</strong> 2<br>
                    <strong>الصلاحيات:</strong> 13
                </div>
            </div>
        </div>
    </div>
    
    <script src="/client/client.js"></script>
    <script>
        // اختبار تحميل الملفات
        document.addEventListener('DOMContentLoaded', function() {
            // اختبار CSS
            const cssTest = document.getElementById('css-test');
            const jsTest = document.getElementById('js-test');
            const overallStatus = document.getElementById('overall-status');
            
            // فحص CSS
            const cssLink = document.querySelector('link[href="/client/client.css"]');
            if (cssLink) {
                cssTest.className = 'test-result test-success';
                cssTest.innerHTML = '<span class="status-indicator status-ok"></span>ملف CSS تم تحميله بنجاح';
            } else {
                cssTest.className = 'test-result test-error';
                cssTest.innerHTML = '<span class="status-indicator status-error"></span>فشل في تحميل ملف CSS';
            }
            
            // فحص JavaScript
            if (typeof LegalSystem !== 'undefined') {
                jsTest.className = 'test-result test-success';
                jsTest.innerHTML = '<span class="status-indicator status-ok"></span>ملف JavaScript تم تحميله بنجاح';
            } else {
                jsTest.className = 'test-result test-warning';
                jsTest.innerHTML = '<span class="status-indicator status-warning"></span>ملف JavaScript محمل جزئياً';
            }
            
            // الحالة العامة
            setTimeout(function() {
                overallStatus.className = 'test-result test-success';
                overallStatus.innerHTML = '<span class="status-indicator status-ok"></span>النظام يعمل بشكل طبيعي - جاهز للاستخدام';
            }, 1000);
            
            // إضافة معالجات للروابط
            const testLinks = document.querySelectorAll('.test-link');
            testLinks.forEach(function(link) {
                link.addEventListener('click', function() {
                    console.log('اختبار الرابط:', link.href);
                });
            });
            
            console.log('🧪 صفحة اختبار النظام القضائي جاهزة');
            console.log('📋 جميع الروابط متاحة للاختبار');
            console.log('✅ النظام يعمل بوضع التطوير');
        });
    </script>
</body>
</html>

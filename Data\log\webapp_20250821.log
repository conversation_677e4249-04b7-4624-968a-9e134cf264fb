2025/08/21 00:23:22.112 ; Info ; 87 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:23:22.126 ; <PERSON> ; 87 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:23:22.150 ; Trace ; 87 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:23:22.155 ; Log ; 87 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:23:22.157 ; Log ; 87 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:23:22.157 ; Log ; 87 ;  ; 0000: ; 21/8/2025 00:23:22 @Http Req#0
2025/08/21 00:23:22.158 ; Log ; 87 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:23:22.158 ; Log ; 87 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:23:22.158 ; Log ; 87 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:23:22.166 ; Log ; 87 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:23:22.170 ; Log ; 87 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:23:22.233 ; Log ; 87 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:23:22.233 ; Log ; 87 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:23:22.277 ; Info ; 87 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:23:22.302 ; Log ; 87 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:23:22.304 ; Log ; 87 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:23:22.309 ; Trace ; 87 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:23:22.324 ; Trace ; 87 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:23:22.324 ; Trace ; 87 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:23:22.325 ; Trace ; 87 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:23:22.327 ; Trace ; 87 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:23:22.332 ; Info ; 87 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:23:22.384 ; Log ; 87 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:23:22.387 ; Log ; 87 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:23:22.434 ; Log ; 87 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:23:22.434 ; Log ; 87 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:23:22.435 ; Log ; 87 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:23:22.438 ; Log ; 87 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:23:22.491 ; Trace ; 87 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:23:22.492 ; Log ; 87 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:23:22.493 ; Trace ; 87 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/21 00:23:22.596 ; Log ; 87 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:23:22.596 ; Log ; 87 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:23:22.596 ; Log ; 87 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:23:22.597 ; Log ; 87 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:23:22.597 ; Log ; 87 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:23:22.597 ; Log ; 87 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:23:22.598 ; Log ; 87 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:23:27.599 ; Log ; 96 ;  ; 0000: ; Started threaded queue processor: DelayedSqlQueue @Http Req#0
2025/08/21 00:23:27.599 ; Log ; 95 ;  ; 0000: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#0
2025/08/21 00:23:27.606 ; Log ; 95 ;  ; 0000: ; Loading scheduled tasks.. @Http Req#0
2025/08/21 00:23:29.479 ; Trace ; 92 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:23:29.487 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:23:29.487 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:23:29.505 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:23:29.540 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:23:29.541 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:23:29.544 ; Log ; 92 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:23:29.560 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:23:29.705 ; Trace ; 92 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:23:29.838 ; Trace ; 92 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:23:30.336 ; Trace ; 92 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:23:30.347 ; Info ; 108 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.366 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.367 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.371 ; Trace ; 108 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.385 ; Trace ; 108 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.385 ; Trace ; 108 ; ::1 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.385 ; Trace ; 108 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.386 ; Trace ; 108 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.388 ; Trace ; 108 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.390 ; Log ; 108 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.414 ; Log ; 108 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.414 ; Trace ; 108 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.424 ; Trace ; 108 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.427 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.427 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.430 ; Log ; 108 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:30.430 ; Info ; 108 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:23:37.829 ; Trace ; 101 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#5 @Req#4 0s
2025/08/21 00:23:37.833 ; Log ; 101 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#5 @Req#4 0s
2025/08/21 00:23:37.833 ; Trace ; 101 ; ::1 ; 9900:admin ; Redirecting user to: /app/?fi-act&cmd=init @Http Req#5 @Req#4 0s
2025/08/21 00:23:37.842 ; Info ; 94 ; ::1 ; 9900:admin ;  ;  ; /app/?fi-act&cmd=init ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 00:23:37.898 ; Info ; 106 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 00:23:41.694 ; Info ; 106 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:23:41.907 ; Trace ; 106 ; ::1 ; 9900:admin ; UserClientSideCache: Add key=acc_data_list @Http Req#8 @Req#7 0s
2025/08/21 00:23:46.797 ; Dev ; 37 ; ::1 ; 9900:admin ; >> Form is cached: hr-wd-def @Http Req#9 @Req#8 0s
2025/08/21 00:23:46.798 ; Info ; 37 ; ::1 ; 9900:admin ; hr-wd-def ;  ; /app/fms/?fm=hr-wd-def&cmd=init ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:23:52.120 ; Log ; 109 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-users ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-users&cmd=view&id=admin @Req#9 0s ; #at:UserError
2025/08/21 00:24:19.936 ; Info ; 90 ; ::1 ; 9900:admin ; sys-cfg ; hs-sys-clt ; /app/fms/?fm=sys-cfg&id=hs-sys-clt&sind=y ; ::1 @Req#10 0s
2025/08/21 00:24:21.657 ; Info ; 90 ; ::1 ; 9900:admin ; sys-cfg ; es-sys-fi-gl ; /app/fms/?fm=sys-cfg&id=es-sys-fi-gl ; ::1 @Req#11 0s
2025/08/21 00:24:24.045 ; Info ; 110 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#12 0s
2025/08/21 00:24:27.632 ; Dev ; 95 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:24:32.998 ; Dev ; 108 ; ::1 ; 9900:admin ; >> Form is cached: hr-emp @Req#13 0s
2025/08/21 00:24:32.998 ; Info ; 108 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#13 0s
2025/08/21 00:25:18.057 ; Trace ; 91 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Req#16 0s
2025/08/21 00:25:18.061 ; Log ; 91 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#16 0s
2025/08/21 00:25:18.061 ; Trace ; 91 ; ::1 ; 9900:admin ; Redirecting user to: /app @Req#16 0s
2025/08/21 00:25:18.071 ; Info ; 101 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#17 0s
2025/08/21 00:25:18.129 ; Info ; 6 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#18 0s
2025/08/21 00:25:27.635 ; Dev ; 95 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:26:27.665 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:26:27.679 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:26:27.799 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:26:27.804 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:26:27.806 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:26:27.806 ; Log ; 1 ;  ; 0000: ; 21/8/2025 00:26:27 @Http Req#0
2025/08/21 00:26:27.806 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:26:27.806 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:26:27.807 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:26:27.809 ; Log ; 1 ;  ; 0000: ; Last shutdown was not clean, check the system @Http Req#0
2025/08/21 00:26:27.812 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:26:27.814 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:26:27.923 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:26:27.926 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:26:27.984 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:26:28.011 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:26:28.016 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:26:28.027 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:26:28.045 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:26:28.045 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:26:28.046 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:26:28.052 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:26:28.059 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:26:28.119 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:26:28.138 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:26:28.227 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:26:28.235 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:26:28.248 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:26:28.271 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:26:28.379 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:26:28.379 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:26:28.379 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:26:28.381 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:26:28.381 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:26:28.387 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:26:28.391 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/21 00:26:28.596 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:26:28.596 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:26:28.597 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:26:28.597 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:26:28.597 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:26:28.598 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:26:28.599 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:26:28.630 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:26:28.635 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:26:28.635 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:26:28.678 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:26:28.711 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:26:28.712 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:26:28.716 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:26:28.725 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:26:28.960 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:26:29.238 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:26:30.076 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:26:30.116 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.132 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.133 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.139 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.159 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.161 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:65 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.162 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.165 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.170 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.175 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.209 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.212 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.234 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.237 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.237 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.244 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:30.244 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:26:33.600 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#4
2025/08/21 00:26:33.600 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#4
2025/08/21 00:26:33.603 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#4
2025/08/21 00:26:40.114 ; Log ; 18 ; ::1 ; 9900: ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Http Req#5 @Req#4 0s ; #at:UserError
2025/08/21 00:26:45.113 ; Log ; 10 ; ::1 ; 9900: ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=list @Http Req#6 @Req#5 0s ; #at:UserError
2025/08/21 00:26:55.883 ; Trace ; 22 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#7 @Req#6 0s
2025/08/21 00:26:55.889 ; Log ; 22 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#7 @Req#6 0s
2025/08/21 00:26:55.889 ; Trace ; 22 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#7 @Req#6 0s
2025/08/21 00:26:55.901 ; Info ; 12 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:26:55.949 ; Info ; 17 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:26:58.662 ; Dev ; 14 ; ::1 ; 9900:admin ; >> Form is cached: hr-emp @Req#9 0s
2025/08/21 00:26:58.662 ; Info ; 14 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=list ; ::1 @Req#9 0s
2025/08/21 00:27:23.534 ; Log ; 11 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hr-e ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hr-e @Req#10 0s ; #at:UserError
2025/08/21 00:27:33.626 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:28:06.218 ; Dev ; 24 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#11 0s
2025/08/21 00:28:06.218 ; Info ; 24 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=list ; ::1 @Req#11 0s
2025/08/21 00:28:11.618 ; Dev ; 9 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#12 0s
2025/08/21 00:28:11.618 ; Info ; 9 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#12 0s
2025/08/21 00:28:33.632 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:28:36.406 ; Dev ; 18 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#13 0s
2025/08/21 00:28:36.406 ; Info ; 18 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#13 0s
2025/08/21 00:28:36.418 ; Error ; 18 ; ::1 ; 9900:admin ;  حقل ( تاريخ التوظيف ) مطلوب <br/>قيمة حقل ( الأجر ) غير مناسبة <br/> @Req#13 0s ; #at:UserError
2025/08/21 00:28:42.034 ; Dev ; 18 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#14 0s
2025/08/21 00:28:42.034 ; Info ; 18 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#14 0s
2025/08/21 00:28:42.037 ; Error ; 18 ; ::1 ; 9900:admin ; قيمة حقل ( الأجر ) غير مناسبة <br/> @Req#14 0s ; #at:UserError
2025/08/21 00:28:49.978 ; Dev ; 16 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#15 0s
2025/08/21 00:28:49.978 ; Info ; 16 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Req#15 0s
2025/08/21 00:28:50.063 ; Trace ; 16 ; ::1 ; 9900:admin ; UserClientSideCache: Invalidate =acc_data_list @Req#15 0s
2025/08/21 00:28:50.075 ; Dev ; 17 ; ::1 ; 9900:admin ; Form loaded from cached: hr-emp @Req#16 0s
2025/08/21 00:28:50.075 ; Info ; 17 ; ::1 ; 9900:admin ; hr-emp ; 8003 ; /app/fms/?fm=hr-emp&cmd=view&id=8003 ; ::1 @Req#16 0s
2025/08/21 00:29:08.503 ; Log ; 38 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#17 0s ; #at:UserError
2025/08/21 00:29:17.229 ; Log ; 17 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#18 0s ; #at:UserError
2025/08/21 00:29:23.752 ; Log ; 18 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#19 0s ; #at:UserError
2025/08/21 00:29:30.051 ; Log ; 27 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#20 0s ; #at:UserError
2025/08/21 00:29:33.633 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:29:36.180 ; Log ; 34 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=add @Req#21 0s ; #at:UserError
2025/08/21 00:29:42.538 ; Log ; 27 ; ::1 ; 9900:admin ; الشاشة أو الصفحة غير معرفة ( hs-menu ) <hr/><a class='link_icon icon_home' href='/app'>الصفحة الرئيسية</a><hr/>/app/fms/?fm=hs-menu&cmd=list @Req#22 0s ; #at:UserError
2025/08/21 00:29:48.260 ; Info ; 17 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#23 0s
2025/08/21 00:30:27.823 ; Trace ; 37 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Req#26 0s
2025/08/21 00:30:27.828 ; Log ; 37 ; ::1 ; 9900:admin ; Successfull user login:admin @Req#26 0s
2025/08/21 00:30:27.828 ; Trace ; 37 ; ::1 ; 9900:admin ; Redirecting user to: /app @Req#26 0s
2025/08/21 00:30:27.838 ; Info ; 31 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Req#27 0s
2025/08/21 00:30:27.894 ; Info ; 17 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Req#28 0s
2025/08/21 00:30:33.635 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:31:19.936 ; Info ; 28 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=sql ; ::1 @Req#29 0s
2025/08/21 00:31:26.220 ; Info ; 15 ; ::1 ; 9900:admin ;  ;  ; /app/?hs-sys-db&cmd=list-tables&sind=y ; ::1 @Req#30 0s
2025/08/21 00:31:33.636 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 

2025/08/21 00:31:53.692 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:31:53.711 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:31:53.826 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:31:53.832 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:31:53.834 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:31:53.834 ; Log ; 1 ;  ; 0000: ; 21/8/2025 00:31:53 @Http Req#0
2025/08/21 00:31:53.835 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:31:53.836 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:31:53.836 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:31:53.837 ; Log ; 1 ;  ; 0000: ; Last shutdown was not clean, check the system @Http Req#0
2025/08/21 00:31:53.841 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:31:53.842 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:31:53.948 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:31:53.950 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:31:54.016 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:31:54.048 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:31:54.055 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:31:54.068 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:31:54.088 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:31:54.088 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:31:54.089 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:31:54.095 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:31:54.103 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:31:54.180 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:31:54.201 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:31:54.317 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:31:54.324 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:31:54.337 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:31:54.354 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:31:54.478 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:31:54.479 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:31:54.479 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:31:54.480 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:31:54.481 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:31:54.486 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:31:54.490 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/21 00:31:54.721 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:31:54.721 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:31:54.722 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:31:54.722 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:31:54.722 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:31:54.723 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:31:54.724 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:31:54.796 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:31:54.804 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:31:54.804 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:31:54.830 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:31:54.875 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:31:54.875 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:31:54.880 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:31:54.881 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:31:55.035 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:31:55.161 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:31:55.583 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:31:55.592 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.605 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.606 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.608 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.620 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.620 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.620 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.622 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.623 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.625 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.648 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.648 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.659 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.662 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.662 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.665 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:55.665 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:31:59.725 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#2
2025/08/21 00:31:59.727 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#2
2025/08/21 00:31:59.730 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#2
2025/08/21 00:32:01.467 ; Trace ; 11 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/21 00:32:01.472 ; Log ; 11 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 00:32:01.473 ; Trace ; 11 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/21 00:32:01.486 ; Info ; 10 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/21 00:32:01.546 ; Info ; 12 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:32:08.497 ; Trace ; 14 ;  ; 9900: ; Session End: User logged out @Http Req#5
2025/08/21 00:32:08.499 ; Log ; 14 ;  ; 9900: ; Initiating app shutdown, waiting for worker threads... @Http Req#5
2025/08/21 00:32:08.507 ; Log ; 8 ;  ; 9900: ; Ending threaded queue: ScheduledTasksQueue @Http Req#5
2025/08/21 00:32:08.507 ; Log ; 7 ;  ; 9900: ; Ending threaded queue: DelayedSqlQueue @Http Req#5
2025/08/21 00:32:08.509 ; Log ; 14 ;  ; 9900: ; ***** App shutdown [HsApp] completed. Bye ******** @Http Req#5

2025/08/21 00:32:30.823 ; Info ; 1 ;  ; : ; LoadCoreAppSettings.. @Http Req#0
2025/08/21 00:32:30.837 ; Dev ; 1 ;  ; 0000: ; DoSystemInit - Dev mode @Http Req#0
2025/08/21 00:32:30.961 ; Trace ; 1 ;  ; 0000: ; min_amount_fraction=0.01 @Http Req#0
2025/08/21 00:32:30.963 ; Log ; 1 ;  ; 0000: ; Starting app [HsApp] @Http Req#0
2025/08/21 00:32:30.963 ; Log ; 1 ;  ; 0000: ; ***** Initializing app [HsApp] ******** @Http Req#0
2025/08/21 00:32:30.963 ; Log ; 1 ;  ; 0000: ; 21/8/2025 00:32:30 @Http Req#0
2025/08/21 00:32:30.963 ; Log ; 1 ;  ; 0000: ; Release: 2.95 on 30/5/2024 @Http Req#0
2025/08/21 00:32:30.964 ; Log ; 1 ;  ; 0000: ; Release Expiry Date: 30/5/2027 Days Left: 647  @Http Req#0
2025/08/21 00:32:30.964 ; Log ; 1 ;  ; 0000: ; DoInitialSystemCheck.. @Http Req#0
2025/08/21 00:32:30.977 ; Log ; 1 ;  ; 0000: ; Loading app modules @Http Req#0
2025/08/21 00:32:30.981 ; Log ; 1 ;  ; 0000: ; Loading module: HSC إدارة النظام @Http Req#0
2025/08/21 00:32:31.046 ; Log ; 1 ;  ; 0000: ; Loading module: CMS نظام إدارة المحتوى @Http Req#0
2025/08/21 00:32:31.047 ; Log ; 1 ;  ; 0000: ; Loading module: FI الحسابات @Http Req#0
2025/08/21 00:32:31.095 ; Info ; 1 ;  ; 0000: ; Init Client: 0000 @Http Req#0
2025/08/21 00:32:31.110 ; Log ; 1 ;  ; 0000: ; Sys client init: Core Module - 0000 @Http Req#0
2025/08/21 00:32:31.114 ; Log ; 1 ;  ; 0000: ; Sys client init: FI-GL Module - 0000 @Http Req#0
2025/08/21 00:32:31.123 ; Trace ; 1 ;  ; 0000: ; Initializing Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:32:31.144 ; Trace ; 1 ;  ; 0000: ; COA.RebuildAccountsTree @Http Req#0
2025/08/21 00:32:31.145 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:32:31.145 ; Trace ; 1 ;  ; 0000: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=0000 @Http Req#0
2025/08/21 00:32:31.150 ; Trace ; 1 ;  ; 0000: ; Accounts loaded:0 @Http Req#0
2025/08/21 00:32:31.159 ; Info ; 1 ;  ; 0000: ; Init Client Completed: 0000 @Http Req#0
2025/08/21 00:32:31.224 ; Log ; 1 ;  ; 0000: ; Loading module: IM إدارة المخزون @Http Req#0
2025/08/21 00:32:31.228 ; Log ; 1 ;  ; 0000: ; Loading module: ES المبيعات @Http Req#0
2025/08/21 00:32:31.261 ; Log ; 1 ;  ; 0000: ; Loading module: PRM إدارة المشتروات @Http Req#0
2025/08/21 00:32:31.261 ; Log ; 1 ;  ; 0000: ; Loading module: HCM شئون الموظفين @Http Req#0
2025/08/21 00:32:31.261 ; Log ; 1 ;  ; 0000: ; Loading module: SS المتجر الإلكتروني @Http Req#0
2025/08/21 00:32:31.263 ; Log ; 1 ;  ; 0000: ; Loading app config @Http Req#0
2025/08/21 00:32:31.297 ; Trace ; 1 ;  ; 0000: ; Load App Active Menu @Http Req#0
2025/08/21 00:32:31.297 ; Log ; 1 ;  ; 0000: ; Init client link adapters @Http Req#0
2025/08/21 00:32:31.298 ; Log ; 1 ;  ; 0000: ; >>> App initialized.. @Http Req#0
2025/08/21 00:32:31.299 ; Log ; 1 ;  ; 0000: ; Db Replication: DISABLED @Http Req#0
2025/08/21 00:32:31.301 ; Log ; 1 ;  ; 0000: ; >>> Init app modules.. @Http Req#0
2025/08/21 00:32:31.307 ; Log ; 1 ;  ; 0000: ; Caching HsStoredObject.. @Http Req#0
2025/08/21 00:32:31.311 ; Trace ; 1 ;  ; 0000: ; HsStoredObject cached: 0 @Http Req#0
2025/08/21 00:32:31.417 ; Log ; 1 ;  ; 0000: ; SMS notif channel NOT enabled @Http Req#0
2025/08/21 00:32:31.417 ; Log ; 1 ;  ; 0000: ; Email notif channel NOT enabled @Http Req#0
2025/08/21 00:32:31.417 ; Log ; 1 ;  ; 0000: ; FCM notif channel NOT enabled @Http Req#0
2025/08/21 00:32:31.417 ; Log ; 1 ;  ; 0000: ; >>> Starting app threads @Http Req#0
2025/08/21 00:32:31.418 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: DelayedSqlQueue @Http Req#0
2025/08/21 00:32:31.418 ; Log ; 1 ;  ; 0000: ; Staring threaded queue: ScheduledTasksQueue @Http Req#0
2025/08/21 00:32:31.419 ; Log ; 1 ;  ; 0000: ; >>> App started @Http Req#0
2025/08/21 00:32:31.483 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles @Http Req#1
2025/08/21 00:32:31.489 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:login @Http Req#1
2025/08/21 00:32:31.489 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:logout @Http Req#1
2025/08/21 00:32:31.516 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-clt-hp @Http Req#1
2025/08/21 00:32:31.560 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:attrs @Http Req#1
2025/08/21 00:32:31.560 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:relation @Http Req#1
2025/08/21 00:32:31.564 ; Log ; 9 ; ::1 ; 0000: ; init adapter:(hs_email) HS Email Notif Channel Adapter, version 1.0 @Http Req#1
2025/08/21 00:32:31.574 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:sys-arch @Http Req#1
2025/08/21 00:32:31.746 ; Trace ; 9 ; ::1 ; 0000: ; Failed to load form:hs-reg-req @Http Req#1
2025/08/21 00:32:31.895 ; Trace ; 9 ; ::1 ; 0000: ; <?xml version="1.0" encoding="utf-16"?>
<HsNamedObjectListOfCustomizedEntry xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <force_unique_obj_id>true</force_unique_obj_id>
  <fail_add_of_exist_id>false</fail_add_of_exist_id>
  <items>
    <CustomizedEntry>
      <id>cash-trf</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-trf</ce_id>
      <entry_title>تحويل/نقل النقدية</entry_title>
      <debit_acc_title>إيداع المبلغ/المقابل في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المسحوب</amount_title>
      <entry_help>نقل مبالغ مالية بين  حسابات النقدية ، مثل الصناديق والخزينة والبنوك</entry_help>
      <doc_type>C00</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cash-dif</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cash-dif</ce_id>
      <entry_title>تقييد العجز في الصندوق</entry_title>
      <debit_acc_title>تقييد العجز على الحساب</debit_acc_title>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الصندوق</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>العجز في الصندوق</entry_memo>
      <amount_title>مبلغ العجز</amount_title>
      <entry_help>تقييد العجز في الصناديق</entry_help>
      <doc_type>C01</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-capital</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-capital</ce_id>
      <entry_title>رفع رأس المال بإستثمار مبلغ مالي</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد الدفعات المالية التي يتم إستثمارها</entry_help>
      <doc_type>C02</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>add-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>add-asset</ce_id>
      <entry_title>تقييد الأصول الموجودة</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>قيمة الأصل</amount_title>
      <entry_help>تقييد الأصول التي تمتلكها المؤسسة و الموجودة سابقا مالم تكن سجلت من قبل، مثل المعدات والأثاث والعقارات</entry_help>
      <doc_type>C03</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>purch-asset</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>purch-asset</ce_id>
      <entry_title>شراء أصول (معدات، أثاث،..) وغيره</entry_title>
      <debit_acc_title>نوع الأصل</debit_acc_title>
      <debit_acc_root>11</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد مشتروات الأصول مثل المعدات والأثاث والعقارات، أو البضاعة التي لغرض إستخدام المؤسسة وليس لغرض البيع</entry_help>
      <doc_type>C04</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-open</ce_id>
      <entry_title>تقييد رصيد سابق لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب راس المال</credit_acc_title>
      <credit_acc_root>211</credit_acc_root>
      <credit_acc_default>2111</credit_acc_default>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للعميل</entry_memo>
      <entry_help>تقييد الارصدة السابقة للعملاء</entry_help>
      <doc_type>C05</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-pay</ce_id>
      <entry_title>دفع مبلغ لعميل</entry_title>
      <debit_acc_title>حساب العميل</debit_acc_title>
      <debit_acc_type>6</debit_acc_type>
      <debit_acc_fixed>true</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_default>teller</credit_acc_default>
      <credit_acc_fixed>true</credit_acc_fixed>
      <entry_memo>دفع مبلغ لعميل</entry_memo>
      <entry_help>مثل إعادة مبلغ لعميل، أو تقديم سلفة لعميل</entry_help>
      <doc_type>C06</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>cust-bad-debit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>cust-bad-debit</ce_id>
      <entry_title>تقييد ديون معدومة</entry_title>
      <debit_acc_title>حساب مصاريف الديون المعدومة</debit_acc_title>
      <debit_acc_root>3</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب العميل</credit_acc_title>
      <credit_acc_type>6</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>ديون معدومة</entry_memo>
      <entry_help>تقييد الديون المعدومة التي لم لا يمكن تحصيلها من العملاء..
 ملاحظة: سيتم تخفيض المبلغ من حساب العميل</entry_help>
      <doc_type>C07</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-open</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-open</ce_id>
      <entry_title>تقييد رصيد سابق لمورد</entry_title>
      <debit_acc_title>حساب راس المال</debit_acc_title>
      <debit_acc_root>211</debit_acc_root>
      <debit_acc_default>2111</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>رصيد سابق للمورد</entry_memo>
      <entry_help>تقييد الارصدة السابقة للموردين</entry_help>
      <doc_type>C08</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>vend-rcpt</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>vend-rcpt</ce_id>
      <entry_title>قبض مبلغ من مورد</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب المورد</credit_acc_title>
      <credit_acc_type>7</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>قبض مبلغ من مورد</entry_memo>
      <entry_help>مثل إستعادة مبلغ من المورد، أو الحصول على سلفة من المورد</entry_help>
      <doc_type>C09</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-debosit</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-debosit</ce_id>
      <entry_title>إيداع مبلغ مالي في حساب جاري الشريك</entry_title>
      <debit_acc_title>تم إيداع المبلغ في حساب</debit_acc_title>
      <debit_acc_root>123</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي أودعها الشريك في حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C10</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>part-withd</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>part-withd</ce_id>
      <entry_title>سحب مبلغ مالي من حساب جاري الشريك</entry_title>
      <debit_acc_title>حساب جاري الشريك</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>تقييد المبالغ التي سحبها الشريك من حسابه ، حساب جاري الشريك، يعتبر مثل الحساب البنكي للشريك، بإمكانه إيداع وسحب المبالغ كما يفعل في البنك</entry_help>
      <doc_type>C11</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>income-dist</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>income-dist</ce_id>
      <entry_title>توزيع الأرباح على الشركاء</entry_title>
      <debit_acc_title>من حساب الأرباح والخسائر</debit_acc_title>
      <debit_acc_root>21</debit_acc_root>
      <debit_acc_default>2131</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>إلى حساب جاري الشريك</credit_acc_title>
      <credit_acc_root>21</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>بعد إغلاق الفترة المالية، يمكن توزيع الأرباح على الشركاء، بعد أن يقوم المحاسب بحساب الربح لكل مشارك حسب النسب المتفق عليها</entry_help>
      <doc_type>C12</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-sal</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-sal</ce_id>
      <entry_title>تقييد إستحقاق لموظف - راتب أو إضافي أو مكافآت أو غيرها</entry_title>
      <debit_acc_title>حساب مصروف الرواتب</debit_acc_title>
      <debit_acc_root>32</debit_acc_root>
      <debit_acc_default>3231</debit_acc_default>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>حساب الموظف</credit_acc_title>
      <credit_acc_type>8</credit_acc_type>
      <credit_acc_fixed>false</credit_acc_fixed>
      <amount_title>المبلغ المستحق للموظف</amount_title>
      <entry_help>
- في هذه الشاشة يتم تقييد المبالغ المستحقة للموظف، مثل الراتب المستحق للشهر الحالي، أو الإضافي أو أي بدلات أو مكافآت وغيرها 
- في هذه العملية  سيتم إيداع المبلغ في حساب الموظف فقط، وليس صرف المبلغ للموظف.
- يمكن لاحقا صرف هذا المبلغ للموظف</entry_help>
      <doc_type>C13</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>emp-pay</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>emp-pay</ce_id>
      <entry_title>صرف مبلغ مالي لموظف</entry_title>
      <debit_acc_title>حساب الموظف</debit_acc_title>
      <debit_acc_type>8</debit_acc_type>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>تم دفع المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_help>صرف مبلغ مالي لموظف/يشمل صرف راتب الموظف أو جزء منه مقدما</entry_help>
      <doc_type>C14</doc_type>
    </CustomizedEntry>
    <CustomizedEntry>
      <id>svc-fill</id>
      <fm_rc>0</fm_rc>
      <needs_refresh>false</needs_refresh>
      <ce_id>svc-fill</ce_id>
      <entry_title>تعبئة رصيد الخدمات</entry_title>
      <debit_acc_title>حساب رصيد الخدمة</debit_acc_title>
      <debit_acc_root>121</debit_acc_root>
      <debit_acc_fixed>false</debit_acc_fixed>
      <credit_acc_title>سحب المبلغ من حساب</credit_acc_title>
      <credit_acc_root>123</credit_acc_root>
      <credit_acc_fixed>false</credit_acc_fixed>
      <entry_memo>تعبئة رصيد الخدمة</entry_memo>
      <doc_type>C15</doc_type>
    </CustomizedEntry>
  </items>
</HsNamedObjectListOfCustomizedEntry> @Http Req#1
2025/08/21 00:32:32.345 ; Trace ; 9 ; ::1 ; 0000: ; Building forms auth roles completed @Http Req#1
2025/08/21 00:32:32.354 ; Info ; 9 ; ::1 ; 9900: ; Init Client: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.368 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: Core Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.369 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: FI-GL Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.373 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.387 ; Trace ; 9 ; ::1 ; 9900: ; COA.RebuildAccountsTree @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.387 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:66 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.387 ; Trace ; 9 ; ::1 ; 9900: ; Initializing Cost Centers Chart Of Accounts: sys_client_id=9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.389 ; Trace ; 9 ; ::1 ; 9900: ; Accounts loaded:0 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.391 ; Trace ; 9 ; ::1 ; 9900: ; Caching Sales Items:9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.393 ; Log ; 9 ; ::1 ; 9900: ; Caching SalesItem.. @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.414 ; Log ; 9 ; ::1 ; 9900: ; Sales Items Cached: 0 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.414 ; Trace ; 9 ; ::1 ; 9900: ; Caching all items stock data.. @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.423 ; Trace ; 9 ; ::1 ; 9900: ; AdjustBizDriversUsingBizProc @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.425 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: ES Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.425 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: HCM Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.428 ; Log ; 9 ; ::1 ; 9900: ; Sys client init: SmartStore Module - 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:32.428 ; Info ; 9 ; ::1 ; 9900: ; Init Client Completed: 9900 @Http Req#2 @Req#1 0s
2025/08/21 00:32:36.317 ; Trace ; 9 ; ::1 ; 9900:admin ; Loading user fav menu:admin @Http Req#3 @Req#2 0s
2025/08/21 00:32:36.322 ; Log ; 9 ; ::1 ; 9900:admin ; Successfull user login:admin @Http Req#3 @Req#2 0s
2025/08/21 00:32:36.322 ; Trace ; 9 ; ::1 ; 9900:admin ; Redirecting user to: /app @Http Req#3 @Req#2 0s
2025/08/21 00:32:36.331 ; Info ; 9 ; ::1 ; 9900:admin ;  ;  ; /app ; ::1 @Http Req#4 @Req#3 0s
2025/08/21 00:32:36.388 ; Info ; 10 ; ::1 ; 9900:admin ;  ;  ; /app/?about&user-login=yes&sind=y ; ::1 @Http Req#5 @Req#4 0s
2025/08/21 00:32:36.419 ; Log ; 8 ;  ; 9900: ; Started threaded queue processor: ScheduledTasksQueue @Http Req#5
2025/08/21 00:32:36.420 ; Log ; 8 ;  ; 9900: ; Loading scheduled tasks.. @Http Req#5
2025/08/21 00:32:36.419 ; Log ; 7 ;  ; 9900: ; Started threaded queue processor: DelayedSqlQueue @Http Req#5
2025/08/21 00:32:41.510 ; Dev ; 12 ; ::1 ; 9900:admin ; >> Form is cached: hr-emp @Http Req#6 @Req#5 0s
2025/08/21 00:32:41.510 ; Info ; 12 ; ::1 ; 9900:admin ; hr-emp ;  ; /app/fms/?fm=hr-emp&cmd=add ; ::1 @Http Req#6 @Req#5 0s
2025/08/21 00:32:46.691 ; Dev ; 22 ; ::1 ; 9900:admin ; >> Form is cached: hr-wd-def @Http Req#7 @Req#6 0s
2025/08/21 00:32:46.691 ; Info ; 22 ; ::1 ; 9900:admin ; hr-wd-def ;  ; /app/fms/?fm=hr-wd-def&cmd=list ; ::1 @Http Req#7 @Req#6 0s
2025/08/21 00:32:46.981 ; Trace ; 22 ; ::1 ; 9900:admin ; UserClientSideCache: Add key=acc_data_list @Http Req#7 @Req#6 0s
2025/08/21 00:33:35.505 ; Info ; 17 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys ; ::1 @Http Req#8 @Req#7 0s
2025/08/21 00:33:36.437 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer:  @Http Req#8

2025/08/21 00:33:39.801 ; Info ; 16 ; ::1 ; 9900:admin ; sys-cfg ; hr-sys ; /app/fms/?fm=sys-cfg&id=hr-sys ; ::1 @Http Req#9 @Req#8 0s
2025/08/21 00:33:39.868 ; Dev ; 16 ; ::1 ; 9900:admin ; GL: OnSystemConfigurationChanged: sys-cfg : hr-sys @Http Req#9 @Req#8 0s
2025/08/21 00:33:39.869 ; Trace ; 16 ; ::1 ; 9900:admin ; Config cache refreshed: hcm-cfg @Http Req#9 @Req#8 0s
2025/08/21 00:34:29.094 ; Info ; 28 ; ::1 ; 9900:admin ;  ; sys-menu ; /app/?menu&id=sys-menu&sind=y ; ::1 @Req#9 0s
2025/08/21 00:34:36.445 ; Dev ; 8 ;  ; 9900: ; Executing Task one-minute-timer: 


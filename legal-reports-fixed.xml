<?xml version="1.0" encoding="UTF-8"?>
<!-- نموذج التقارير القانونية المحدث - متوافق مع نظام RemoX -->
<form-module name="legal-reports" title="التقارير القانونية" icon="chart-bar" 
             permission="legal_reports_view">

    <!-- التقرير اليومي -->
    <report-view name="daily" title="التقرير اليومي" permission="legal_reports_view">
        <parameters>
            <parameter name="report_date" title="تاريخ التقرير" type="date" default="today"/>
            <parameter name="include_follows" title="تضمين المتابعات" type="checkbox" default="true"/>
            <parameter name="include_hearings" title="تضمين الجلسات" type="checkbox" default="true"/>
        </parameters>
        
        <data-source>
            SELECT 
                i.id,
                i.case_number,
                i.title,
                i.client_name,
                i.status,
                i.amount,
                i.next_hearing,
                COUNT(f.id) as today_follows,
                SUM(CASE WHEN f.date_field = @report_date THEN 1 ELSE 0 END) as follows_today,
                i.created_date
            FROM issues i
            LEFT JOIN follows f ON i.id = f.case_id
            WHERE (i.created_date = @report_date 
                   OR i.next_hearing = @report_date
                   OR EXISTS(SELECT 1 FROM follows WHERE case_id = i.id AND date_field = @report_date))
            GROUP BY i.id, i.case_number, i.title, i.client_name, i.status, i.amount, i.next_hearing, i.created_date
            ORDER BY i.case_number
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="title" title="عنوان القضية" width="200"/>
            <column name="client_name" title="الموكل" width="150"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="amount" title="المبلغ" width="120" format="currency"/>
            <column name="next_hearing" title="الجلسة القادمة" width="120" format="date"/>
            <column name="follows_today" title="متابعات اليوم" width="100" format="number"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <summary>
            <field name="total_cases" title="إجمالي القضايا" type="count"/>
            <field name="total_amount" title="إجمالي المبالغ" type="sum" column="amount" format="currency"/>
            <field name="total_follows" title="إجمالي المتابعات" type="sum" column="follows_today"/>
        </summary>
    </report-view>

    <!-- تقرير ملخص القضايا -->
    <report-view name="cases-summary" title="ملخص القضايا" permission="legal_reports_view">
        <parameters>
            <parameter name="date_from" title="من تاريخ" type="date" default="first_day_of_month"/>
            <parameter name="date_to" title="إلى تاريخ" type="date" default="today"/>
            <parameter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="active">نشطة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </parameter>
        </parameters>
        
        <data-source>
            SELECT 
                i.id,
                i.case_number,
                i.title,
                i.client_name,
                i.status,
                i.amount,
                i.contract_method,
                COUNT(f.id) as follows_count,
                SUM(CASE WHEN f.status = 'completed' THEN 1 ELSE 0 END) as completed_follows,
                i.next_hearing,
                i.created_date
            FROM issues i
            LEFT JOIN follows f ON i.id = f.case_id
            WHERE (@date_from IS NULL OR i.created_date >= @date_from)
              AND (@date_to IS NULL OR i.created_date <= @date_to)
              AND (@status = '' OR i.status = @status)
            GROUP BY i.id, i.case_number, i.title, i.client_name, i.status, i.amount, i.contract_method, i.next_hearing, i.created_date
            ORDER BY i.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="title" title="العنوان" width="200"/>
            <column name="client_name" title="الموكل" width="150"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="amount" title="المبلغ" width="120" format="currency"/>
            <column name="contract_method" title="طريقة التعاقد" width="100"/>
            <column name="follows_count" title="المتابعات" width="80" format="number"/>
            <column name="completed_follows" title="المكتملة" width="80" format="number"/>
            <column name="next_hearing" title="الجلسة القادمة" width="120" format="date"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="date"/>
        </columns>
        
        <summary>
            <field name="total_cases" title="إجمالي القضايا" type="count"/>
            <field name="total_amount" title="إجمالي المبالغ" type="sum" column="amount" format="currency"/>
            <field name="avg_amount" title="متوسط المبلغ" type="avg" column="amount" format="currency"/>
            <field name="total_follows" title="إجمالي المتابعات" type="sum" column="follows_count"/>
            <field name="active_cases" title="القضايا النشطة" type="count" condition="status = 'active'"/>
            <field name="completed_cases" title="القضايا المكتملة" type="count" condition="status = 'completed'"/>
        </summary>
    </report-view>
    
    <!-- تقرير المتابعات -->
    <report-view name="follows-report" title="تقرير المتابعات" permission="legal_reports_view">
        <parameters>
            <parameter name="date_from" title="من تاريخ" type="date" default="first_day_of_month"/>
            <parameter name="date_to" title="إلى تاريخ" type="date" default="today"/>
            <parameter name="status" title="الحالة" type="select">
                <options>
                    <option value="">الكل</option>
                    <option value="pending">معلقة</option>
                    <option value="completed">مكتملة</option>
                    <option value="cancelled">ملغية</option>
                </options>
            </parameter>
        </parameters>
        
        <data-source>
            SELECT 
                f.id,
                i.case_number,
                i.title as case_title,
                i.client_name,
                f.report,
                f.date_field,
                f.status,
                f.created_date,
                i.amount as case_amount
            FROM follows f
            JOIN issues i ON f.case_id = i.id
            WHERE (@date_from IS NULL OR f.date_field >= @date_from)
              AND (@date_to IS NULL OR f.date_field <= @date_to)
              AND (@status = '' OR f.status = @status)
            ORDER BY f.date_field DESC, f.created_date DESC
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="case_title" title="عنوان القضية" width="200"/>
            <column name="client_name" title="الموكل" width="120"/>
            <column name="report" title="التقرير" width="300"/>
            <column name="date_field" title="تاريخ المتابعة" width="120" format="date"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="case_amount" title="مبلغ القضية" width="120" format="currency"/>
            <column name="created_date" title="تاريخ الإنشاء" width="120" format="datetime"/>
        </columns>
        
        <summary>
            <field name="total_follows" title="إجمالي المتابعات" type="count"/>
            <field name="completed_follows" title="المتابعات المكتملة" type="count" condition="status = 'completed'"/>
            <field name="pending_follows" title="المتابعات المعلقة" type="count" condition="status = 'pending'"/>
            <field name="total_case_amount" title="إجمالي مبالغ القضايا" type="sum" column="case_amount" format="currency"/>
        </summary>
    </report-view>

    <!-- تقرير الجلسات -->
    <report-view name="hearings-report" title="تقرير الجلسات" permission="legal_reports_view">
        <parameters>
            <parameter name="date_from" title="من تاريخ" type="date" default="today"/>
            <parameter name="date_to" title="إلى تاريخ" type="date" default="today"/>
        </parameters>
        
        <data-source>
            SELECT 
                i.id,
                i.case_number,
                i.title,
                i.client_name,
                i.status,
                i.amount,
                i.next_hearing,
                i.court_name,
                i.created_date
            FROM issues i
            WHERE i.next_hearing BETWEEN @date_from AND @date_to
            ORDER BY i.next_hearing, i.case_number
        </data-source>
        
        <columns>
            <column name="case_number" title="رقم القضية" width="120"/>
            <column name="title" title="عنوان القضية" width="200"/>
            <column name="client_name" title="الموكل" width="150"/>
            <column name="court_name" title="المحكمة" width="150"/>
            <column name="next_hearing" title="موعد الجلسة" width="120" format="date"/>
            <column name="status" title="الحالة" width="100" format="status"/>
            <column name="amount" title="المبلغ" width="120" format="currency"/>
        </columns>
        
        <summary>
            <field name="total_hearings" title="إجمالي الجلسات" type="count"/>
            <field name="total_amount" title="إجمالي المبالغ" type="sum" column="amount" format="currency"/>
        </summary>
    </report-view>

    <!-- قائمة التقارير -->
    <list-view name="list" title="قائمة التقارير القانونية" permission="legal_reports_view">
        <static-data>
            <row>
                <field name="report_name">التقرير اليومي</field>
                <field name="report_description">تقرير يومي للقضايا والمتابعات</field>
                <field name="report_url">/app/fms/?fm=legal-reports&amp;cmd=daily</field>
            </row>
            <row>
                <field name="report_name">ملخص القضايا</field>
                <field name="report_description">تقرير شامل لجميع القضايا</field>
                <field name="report_url">/app/fms/?fm=legal-reports&amp;cmd=cases-summary</field>
            </row>
            <row>
                <field name="report_name">تقرير المتابعات</field>
                <field name="report_description">تقرير تفصيلي للمتابعات</field>
                <field name="report_url">/app/fms/?fm=legal-reports&amp;cmd=follows-report</field>
            </row>
            <row>
                <field name="report_name">تقرير الجلسات</field>
                <field name="report_description">تقرير الجلسات القادمة</field>
                <field name="report_url">/app/fms/?fm=legal-reports&amp;cmd=hearings-report</field>
            </row>
        </static-data>
        
        <columns>
            <column name="report_name" title="اسم التقرير" width="200"/>
            <column name="report_description" title="الوصف" width="300"/>
        </columns>
        
        <actions>
            <action name="view_report" title="عرض التقرير" icon="eye" color="primary" url="{report_url}"/>
        </actions>
    </list-view>
</form-module>
